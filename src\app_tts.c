//
// Created by liang on 2025/7/20.
//
#include "logger.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <signal.h>
#include <time.h>
#include <unistd.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include "app_tts.h"
#include "app_config.h"

/**
 * 调用TTS服务
 */
int call_tts_service(const char *text) {
    if (!text || strlen(text) == 0) {
        return -1;
    }
#if TTS_API_ENABLE
    printf("🔊 Calling TTS for: %s\n", text);

    CURL *curl = curl_easy_init();
    if (!curl) {
        printf("❌ Failed to initialize CURL for TTS\n");
        return -1;
    }

    // 创建JSON payload
    json_object *root = json_object_new_object();
    json_object *text_obj = json_object_new_string(text);
    json_object *immediate_obj = json_object_new_boolean(1);

    json_object_object_add(root, "text", text_obj);
    json_object_object_add(root, "immediate", immediate_obj);

    const char *json_string = json_object_to_json_string(root);

    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_URL, "http://192.168.6.48:8230/speak");
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_string);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);

    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json");
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    long http_code = 0;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);

    // 清理资源
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    json_object_put(root);

    if (res != CURLE_OK) {
        printf("❌ TTS request failed: %s\n", curl_easy_strerror(res));
        return -1;
    }

    if (http_code != 200) {
        printf("❌ TTS HTTP error: %ld\n", http_code);
        return -1;
    }

    printf("✅ TTS request successful\n");
#endif
    return 0;
}
