#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <speex/speex_echo.h>
#include <speex/speex_preprocess.h>

// 音频参数
#define SAMPLE_RATE 48000
#define FRAME_SIZE 480      // 10ms @ 48kHz
#define TAIL_LENGTH 14400    // 200ms @ 48kHz
#define CHANNELS 2          // 立体声

// 文件路径
#define MIC_FILE "../audio_pcm_demo/mic48k-1ch.pcm"
#define REF_FILE "../audio_pcm_demo/ref48k-1ch.pcm"
#define OUTPUT_FILE "../audio_pcm_demo/output.pcm"

/**
 * 立体声回声消除demo
 */
int main() {
    FILE *mic_file = NULL;
    FILE *ref_file = NULL;
    FILE *output_file = NULL;
    
    SpeexEchoState *echo_state[CHANNELS];
    SpeexPreprocessState *preprocess_state = NULL;
    
    int16_t mic_buffer[FRAME_SIZE];
    int16_t ref_buffer[FRAME_SIZE];
    int16_t output_buffer[FRAME_SIZE];
    int16_t processed_buffer[FRAME_SIZE];
    
    int frames_processed = 0;
    int ret = 0;
    
    printf("=== 立体声回声消除Demo ===\n");
    printf("输入文件: %s\n", MIC_FILE);
    printf("参考文件: %s\n", REF_FILE);
    printf("输出文件: %s\n", OUTPUT_FILE);
    printf("参数: %dHz, 帧大小=%d, 尾长=%d\n", SAMPLE_RATE, FRAME_SIZE, TAIL_LENGTH);
    
    // 打开文件
    mic_file = fopen(MIC_FILE, "rb");
    if (!mic_file) {
        fprintf(stderr, "错误: 无法打开麦克风文件 %s\n", MIC_FILE);
        ret = -1;
        goto cleanup;
    }
    
    ref_file = fopen(REF_FILE, "rb");
    if (!ref_file) {
        fprintf(stderr, "错误: 无法打开参考文件 %s\n", REF_FILE);
        ret = -1;
        goto cleanup;
    }

    //// 在进入 while(1) 循环前，偏移 100ms
    //const int delay_ms = 90;
    //const int delay_samples = (delay_ms * SAMPLE_RATE) / 1000;      // 4800 samples
    //const long delay_bytes   = delay_samples * sizeof(int16_t);     // 4800 * 2 = 9600 bytes
    //printf("delay_ms: %d", delay_ms);
    //
    //// 从文件头开始，跳过这段延迟
    //if (fseek(ref_file, delay_bytes, SEEK_SET) != 0) {
    //    perror("fseek failed");
    //    // 根据需要决定是否退出
    //}
    
    output_file = fopen(OUTPUT_FILE, "wb");
    if (!output_file) {
        fprintf(stderr, "错误: 无法创建输出文件 %s\n", OUTPUT_FILE);
        ret = -1;
        goto cleanup;
    }
    
    // 初始化回声消除器（单声道，因为输入文件是单声道）
    echo_state[0] = speex_echo_state_init(FRAME_SIZE, TAIL_LENGTH);
    if (!echo_state[0]) {
        fprintf(stderr, "错误: 无法初始化回声消除器\n");
        ret = -1;
        goto cleanup;
    }
    
    // 设置采样率
    int sample_rate = SAMPLE_RATE;
    speex_echo_ctl(echo_state[0], SPEEX_ECHO_SET_SAMPLING_RATE, &sample_rate);
    
    // 初始化预处理器（用于噪声抑制）
    preprocess_state = speex_preprocess_state_init(FRAME_SIZE, SAMPLE_RATE);
    if (!preprocess_state) {
        fprintf(stderr, "错误: 无法初始化预处理器\n");
        ret = -1;
        goto cleanup;
    }
    
    // 关联回声消除器
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_STATE, echo_state[0]);
    
    // 配置噪声抑制
    int denoise = 1;
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_DENOISE, &denoise);
    int noise_suppress = -15;
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noise_suppress);
    
    // 配置残余回声抑制
    int echo_suppress = -40;
    int echo_suppress_active = -50;
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_SUPPRESS, &echo_suppress);
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_SUPPRESS_ACTIVE, &echo_suppress_active);
    
    printf("\n开始处理音频...\n");
    
    // 处理音频数据
    while (1) {
        // 读取麦克风数据
        size_t mic_read = fread(mic_buffer, sizeof(int16_t), FRAME_SIZE, mic_file);
        if (mic_read != FRAME_SIZE) {
            if (feof(mic_file)) {
                printf("麦克风文件读取完成\n");
                break;
            } else {
                fprintf(stderr, "错误: 读取麦克风文件失败\n");
                ret = -1;
                goto cleanup;
            }
        }

        //const float ref_gain = 8.0f;
        //for (int i = 0; i < FRAME_SIZE; i++) {
        //    float scaled = mic_buffer[i] * ref_gain;
        //    // 防止溢出
        //    if (scaled >  32767.0f) scaled =  32767.0f;
        //    if (scaled < -32768.0f) scaled = -32768.0f;
        //    mic_buffer[i] = (int16_t)scaled;
        //}

        // 读取参考信号
        size_t ref_read = fread(ref_buffer, sizeof(int16_t), FRAME_SIZE, ref_file);
        if (ref_read != FRAME_SIZE) {
            if (feof(ref_file)) {
                // 参考文件结束，用静音填充
                memset(ref_buffer, 0, FRAME_SIZE * sizeof(int16_t));
            } else {
                fprintf(stderr, "错误: 读取参考文件失败\n");
                ret = -1;
                goto cleanup;
            }
        }

        // —— 在送入 AEC 之前衰减参考信号 ——
        // 这里以 0.3 倍增益为例（原来电平的 30%）

        const float ref_gain = 0.5f;
        //printf("set ref_gain %0.3f \n",ref_gain);
        for (int i = 0; i < FRAME_SIZE; i++) {
            float scaled = ref_buffer[i] * ref_gain;
            // 防止溢出
            if (scaled >  32767.0f) scaled =  32767.0f;
            if (scaled < -32768.0f) scaled = -32768.0f;
            ref_buffer[i] = (int16_t)scaled;
        }

        
        // 执行回声消除
        speex_echo_cancellation(echo_state[0], mic_buffer, ref_buffer, output_buffer);

        //float erl, erle;
        //speex_echo_ctl(echo_state[0], SPEEX_ECHO_GET_ERL,  &erl);
        //speex_echo_ctl(echo_state[0], SPEEX_ECHO_GET_ERLE, &erle);
        //printf("ERL=%.1f dB, ERLE=%.1f dB\n", erl, erle);

        // 执行预处理（噪声抑制和残余回声抑制）
        memcpy(processed_buffer, output_buffer, FRAME_SIZE * sizeof(int16_t));
        speex_preprocess_run(preprocess_state, processed_buffer);
        
        // 写入输出文件
        size_t written = fwrite(processed_buffer, sizeof(int16_t), FRAME_SIZE, output_file);
        if (written != FRAME_SIZE) {
            fprintf(stderr, "错误: 写入输出文件失败\n");
            ret = -1;
            goto cleanup;
        }

        frames_processed++;
        
        // 每1000帧打印一次进度
        if (frames_processed % 1000 == 0) {
            float seconds = (float)frames_processed * FRAME_SIZE / SAMPLE_RATE;
            printf("已处理: %d 帧 (%.2f 秒)\n", frames_processed, seconds);
        }
    }
    
    float total_seconds = (float)frames_processed * FRAME_SIZE / SAMPLE_RATE;
    printf("\n处理完成!\n");
    printf("总帧数: %d\n", frames_processed);
    printf("总时长: %.2f 秒\n", total_seconds);
    printf("输出文件: %s\n", OUTPUT_FILE);
    
cleanup:
    // 清理资源
    if (echo_state[0]) {
        speex_echo_state_destroy(echo_state[0]);
    }
    if (preprocess_state) {
        speex_preprocess_state_destroy(preprocess_state);
    }
    if (mic_file) fclose(mic_file);
    if (ref_file) fclose(ref_file);
    if (output_file) fclose(output_file);
    
    return ret;
}
