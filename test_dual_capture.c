#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <alsa/asoundlib.h>
#include <time.h>
#include <signal.h>
#include <math.h>

// 配置参数 - 根据实际系统调整
#define MIC_DEVICE "plughw:1,0"      // 麦克风设备
#define SAMPLE_RATE 48000            // 统一采样率 48kHz
#define CHANNELS 2                   // 采集声道数（立体声）
#define SAVE_CHANNELS 1              // 保存声道数（只保存左声道）
#define FRAME_SIZE 480               // 每次读取的帧数（10ms @ 48kHz）
#define CAPTURE_SECONDS 50           // 采集时长

// 参考信号设备候选列表（按优先级顺序）
const char *ref_devices[] = {
    "pulse",                // PulseAudio 默认设备
    "hw:3,1",              // Loopback 设备
    "plughw:0,0",          // 主音频设备（带插件支持）
    "hw:0,0",              // 主音频设备
    "default",             // 默认设备
    "plughw:2,0",          // 原始设备作为备选
    NULL
};

// 全局变量
volatile int running = 1;
FILE *mic_file = NULL;
FILE *ref_file = NULL;
pthread_mutex_t file_mutex = PTHREAD_MUTEX_INITIALIZER;

// 统计信息
typedef struct {
    unsigned long frames_captured;
    unsigned long bytes_written;
    unsigned long underruns;
    unsigned long overruns;
    struct timespec start_time;
} capture_stats_t;

capture_stats_t mic_stats = {0};
capture_stats_t ref_stats = {0};

// 信号处理
void signal_handler(int sig) {
    printf("\n收到信号 %d，停止采集...\n", sig);
    running = 0;
}

// 尝试打开参考信号设备
int setup_ref_device(snd_pcm_t **handle) {
    int err;

    printf("🔍 尝试打开参考信号设备...\n");

    for (int i = 0; ref_devices[i] != NULL; i++) {
        printf("🔧 尝试设备: %s\n", ref_devices[i]);

        err = snd_pcm_open(handle, ref_devices[i], SND_PCM_STREAM_CAPTURE, 0);
        if (err >= 0) {
            printf("✅ 成功打开设备: %s\n", ref_devices[i]);

            // 继续设置设备参数
            if (setup_alsa_device_params(*handle, ref_devices[i]) == 0) {
                return 0; // 成功
            } else {
                printf("❌ 设备参数设置失败: %s\n", ref_devices[i]);
                snd_pcm_close(*handle);
                *handle = NULL;
            }
        } else {
            printf("⚠️  无法打开设备 %s: %s\n", ref_devices[i], snd_strerror(err));
        }
    }

    printf("❌ 所有参考信号设备都无法打开\n");
    return -1;
}

// 设置ALSA设备参数
int setup_alsa_device_params(snd_pcm_t *handle, const char *device_name) {
    int err;
    snd_pcm_hw_params_t *hw_params;
    snd_pcm_format_t format = SND_PCM_FORMAT_S16_LE;

    printf("🔧 设置设备参数: %s\n", device_name);

    // 分配硬件参数结构
    snd_pcm_hw_params_alloca(&hw_params);

    // 初始化硬件参数
    if ((err = snd_pcm_hw_params_any(handle, hw_params)) < 0) {
        fprintf(stderr, "❌ 无法初始化硬件参数: %s\n", snd_strerror(err));
        return -1;
    }

    // 设置访问模式
    if ((err = snd_pcm_hw_params_set_access(handle, hw_params,
                                           SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        fprintf(stderr, "❌ 无法设置访问模式: %s\n", snd_strerror(err));
        return -1;
    }

    // 设置格式
    if ((err = snd_pcm_hw_params_set_format(handle, hw_params, format)) < 0) {
        fprintf(stderr, "❌ 无法设置格式: %s\n", snd_strerror(err));
        return -1;
    }

    // 设置采样率
    unsigned int actual_rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(handle, hw_params,
                                              &actual_rate, 0)) < 0) {
        fprintf(stderr, "❌ 无法设置采样率: %s\n", snd_strerror(err));
        return -1;
    }

    if (actual_rate != SAMPLE_RATE) {
        printf("⚠️  实际采样率 %d 与请求的 %d 不同\n", actual_rate, SAMPLE_RATE);
    }

    // 设置声道数
    if ((err = snd_pcm_hw_params_set_channels(handle, hw_params, CHANNELS)) < 0) {
        fprintf(stderr, "❌ 无法设置声道数: %s\n", snd_strerror(err));
        return -1;
    }

    // 设置缓冲区大小
    snd_pcm_uframes_t buffer_size = FRAME_SIZE * 8;
    if ((err = snd_pcm_hw_params_set_buffer_size_near(handle, hw_params,
                                                     &buffer_size)) < 0) {
        fprintf(stderr, "❌ 无法设置缓冲区大小: %s\n", snd_strerror(err));
        return -1;
    }

    // 应用硬件参数
    if ((err = snd_pcm_hw_params(handle, hw_params)) < 0) {
        fprintf(stderr, "❌ 无法应用硬件参数: %s\n", snd_strerror(err));
        return -1;
    }

    // 准备设备
    if ((err = snd_pcm_prepare(handle)) < 0) {
        fprintf(stderr, "❌ 无法准备设备: %s\n", snd_strerror(err));
        return -1;
    }

    printf("✅ 设备参数设置完成: %s (%dHz, %d声道)\n", device_name, SAMPLE_RATE, CHANNELS);
    return 0;
}

// 设置ALSA设备
int setup_alsa_device(snd_pcm_t **handle, const char *device) {
    int err;

    printf("🔧 设置设备: %s\n", device);

    // 打开设备
    if ((err = snd_pcm_open(handle, device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        fprintf(stderr, "❌ 无法打开设备 %s: %s\n", device, snd_strerror(err));
        return -1;
    }

    // 设置设备参数
    if (setup_alsa_device_params(*handle, device) < 0) {
        snd_pcm_close(*handle);
        *handle = NULL;
        return -1;
    }

    return 0;
}

// 同步采集线程 - 同时采集麦克风和参考信号
void *synchronized_capture_thread(void *arg) {
    snd_pcm_t *mic_handle = NULL, *ref_handle = NULL;
    int16_t mic_buffer[FRAME_SIZE * CHANNELS];
    int16_t ref_buffer[FRAME_SIZE * CHANNELS];
    int mic_err, ref_err;
    unsigned long frame_count = 0;

    printf("🎯 同步采集线程启动\n");

    // 设置麦克风设备
    if (setup_alsa_device(&mic_handle, MIC_DEVICE) < 0) {
        printf("❌ 麦克风设备设置失败\n");
        return NULL;
    }

    // 设置参考信号设备（尝试多个候选设备）
    if (setup_ref_device(&ref_handle) < 0) {
        printf("❌ 参考信号设备设置失败\n");
        if (mic_handle) snd_pcm_close(mic_handle);
        return NULL;
    }

    // 记录开始时间
    clock_gettime(CLOCK_MONOTONIC, &mic_stats.start_time);
    clock_gettime(CLOCK_MONOTONIC, &ref_stats.start_time);

    printf("🚀 开始同步采集...\n");

    while (running) {
        // 同时读取麦克风和参考信号
        mic_err = snd_pcm_readi(mic_handle, mic_buffer, FRAME_SIZE);
        ref_err = snd_pcm_readi(ref_handle, ref_buffer, FRAME_SIZE);

        // 处理麦克风错误
        if (mic_err == -EPIPE) {
            printf("🔄 麦克风下溢，恢复中...\n");
            snd_pcm_prepare(mic_handle);
            mic_stats.underruns++;
            continue;
        } else if (mic_err < 0) {
            fprintf(stderr, "❌ 麦克风读取错误: %s\n", snd_strerror(mic_err));
            if (snd_pcm_recover(mic_handle, mic_err, 0) < 0) break;
            continue;
        }

        // 处理参考信号错误
        if (ref_err == -EPIPE) {
            printf("🔄 参考信号下溢，恢复中...\n");
            snd_pcm_prepare(ref_handle);
            ref_stats.underruns++;
            // 不要continue，继续处理麦克风数据
        } else if (ref_err < 0) {
            fprintf(stderr, "⚠️  参考信号读取错误: %s\n", snd_strerror(ref_err));
            snd_pcm_recover(ref_handle, ref_err, 0);
            // 不要continue，继续处理麦克风数据
        }

        // 写入文件（加锁保证同步）
        pthread_mutex_lock(&file_mutex);

        // 写入麦克风数据（只保存左声道）
        if (mic_file && mic_err > 0) {
            // 提取左声道数据
            int16_t mic_left_channel[FRAME_SIZE];
            for (int i = 0; i < mic_err; i++) {
                mic_left_channel[i] = mic_buffer[i * CHANNELS]; // 只取左声道（索引0）
            }

            size_t mic_written = fwrite(mic_left_channel, sizeof(int16_t), mic_err, mic_file);
            if (mic_written == mic_err) {
                mic_stats.frames_captured += mic_err;
                mic_stats.bytes_written += mic_written * sizeof(int16_t);
            } else {
                mic_stats.overruns++;
            }
        }

        // 写入参考信号数据（只保存左声道）
        if (ref_file && ref_err > 0) {
            // 提取左声道数据
            int16_t ref_left_channel[FRAME_SIZE];
            for (int i = 0; i < ref_err; i++) {
                ref_left_channel[i] = ref_buffer[i * CHANNELS]; // 只取左声道（索引0）
            }

            size_t ref_written = fwrite(ref_left_channel, sizeof(int16_t), ref_err, ref_file);
            if (ref_written == ref_err) {
                ref_stats.frames_captured += ref_err;
                ref_stats.bytes_written += ref_written * sizeof(int16_t);
            } else {
                ref_stats.overruns++;
            }
        }

        pthread_mutex_unlock(&file_mutex);

        frame_count++;

        // 每4800帧显示一次进度（约10秒 @ 48kHz）
        if (frame_count % 4800 == 0) {
            float seconds = (float)frame_count * FRAME_SIZE / SAMPLE_RATE;
            printf("📊 已采集 %.1f秒 | 麦克风: %lu帧 | 参考: %lu帧\n",
                   seconds, mic_stats.frames_captured, ref_stats.frames_captured);
        }
    }

    // 清理资源
    if (mic_handle) snd_pcm_close(mic_handle);
    if (ref_handle) snd_pcm_close(ref_handle);

    printf("🎯 同步采集线程结束\n");
    return NULL;
}

int main() {
    pthread_t capture_thread;
    time_t start_time, current_time;
    
    printf("🎯 双路音频同步采集测试程序\n");
    printf("采集时长: %d秒\n", CAPTURE_SECONDS);
    printf("麦克风: %s (%dkHz, %d声道采集 → %d声道保存)\n", MIC_DEVICE, SAMPLE_RATE/1000, CHANNELS, SAVE_CHANNELS);
    printf("参考信号: 自动检测设备 (%dkHz, %d声道采集 → %d声道保存)\n", SAMPLE_RATE/1000, CHANNELS, SAVE_CHANNELS);
    printf("候选设备: ");
    for (int i = 0; ref_devices[i] != NULL; i++) {
        printf("%s%s", ref_devices[i], ref_devices[i+1] ? ", " : "");
    }
    printf("\n按 Ctrl+C 提前停止\n\n");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 打开输出文件
    mic_file = fopen("mic_capture_50s.pcm", "wb");
    if (!mic_file) {
        fprintf(stderr, "❌ 无法创建麦克风文件\n");
        return -1;
    }
    
    ref_file = fopen("ref_capture_50s.pcm", "wb");
    if (!ref_file) {
        fprintf(stderr, "❌ 无法创建参考信号文件\n");
        fclose(mic_file);
        return -1;
    }
    
    printf("✅ 输出文件创建成功\n");
    printf("📁 mic_capture_50s.pcm - 麦克风数据\n");
    printf("📁 ref_capture_50s.pcm - 参考信号数据\n\n");
    
    // 启动同步采集线程
    if (pthread_create(&capture_thread, NULL, synchronized_capture_thread, NULL) != 0) {
        fprintf(stderr, "❌ 无法创建同步采集线程\n");
        goto cleanup;
    }
    
    printf("🚀 采集开始...\n\n");
    
    // 等待指定时间或用户中断
    start_time = time(NULL);
    while (running && (time(NULL) - start_time) < CAPTURE_SECONDS) {
        sleep(1);
        current_time = time(NULL);
        int elapsed = current_time - start_time;
        int remaining = CAPTURE_SECONDS - elapsed;
        
        if (elapsed % 10 == 0 && elapsed > 0) {
            printf("⏱️ 已采集 %d 秒，剩余 %d 秒\n", elapsed, remaining);
        }
    }
    
    // 停止采集
    running = 0;
    printf("\n🛑 停止采集...\n");
    
    // 等待线程结束
    pthread_join(capture_thread, NULL);
    
cleanup:
    // 关闭文件
    if (mic_file) {
        fclose(mic_file);
    }
    if (ref_file) {
        fclose(ref_file);
    }
    
    // 显示统计信息
    printf("\n📊 采集统计:\n");
    printf("麦克风:\n");
    printf("  - 帧数: %lu\n", mic_stats.frames_captured);
    printf("  - 字节数: %lu\n", mic_stats.bytes_written);
    printf("  - 时长: %.2f秒\n", (float)mic_stats.frames_captured / SAMPLE_RATE);
    printf("  - 下溢: %lu\n", mic_stats.underruns);
    printf("  - 溢出: %lu\n", mic_stats.overruns);
    
    printf("参考信号:\n");
    printf("  - 帧数: %lu\n", ref_stats.frames_captured);
    printf("  - 字节数: %lu\n", ref_stats.bytes_written);
    printf("  - 时长: %.2f秒\n", (float)ref_stats.frames_captured / SAMPLE_RATE);
    printf("  - 下溢: %lu\n", ref_stats.underruns);
    printf("  - 溢出: %lu\n", ref_stats.overruns);

    // 计算同步性
    float mic_duration = (float)mic_stats.frames_captured / SAMPLE_RATE;
    float ref_duration = (float)ref_stats.frames_captured / SAMPLE_RATE;
    float sync_diff = fabs(mic_duration - ref_duration);

    printf("\n🔍 同步性分析:\n");
    printf("  - 麦克风时长: %.3f秒\n", mic_duration);
    printf("  - 参考信号时长: %.3f秒\n", ref_duration);
    printf("  - 时长差异: %.3f秒 (%.1fms)\n", sync_diff, sync_diff * 1000);

    if (sync_diff < 0.001) {
        printf("  - 同步状态: ✅ 优秀 (<1ms)\n");
    } else if (sync_diff < 0.010) {
        printf("  - 同步状态: ✅ 良好 (<10ms)\n");
    } else if (sync_diff < 0.050) {
        printf("  - 同步状态: ⚠️  一般 (<50ms)\n");
    } else {
        printf("  - 同步状态: ❌ 较差 (>50ms)\n");
    }

    printf("\n✅ 采集完成！\n");
    printf("📁 生成文件:\n");
    printf("  - mic_capture_50s.pcm (%dkHz, %d声道, 16bit) - 左声道数据\n", SAMPLE_RATE/1000, SAVE_CHANNELS);
    printf("  - ref_capture_50s.pcm (%dkHz, %d声道, 16bit) - 左声道数据\n", SAMPLE_RATE/1000, SAVE_CHANNELS);
    
    return 0;
}
