<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AEC WebSocket 控制面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            min-height: 80vh;
        }

        .left-panel {
            width: 400px;
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }

        .right-panel {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-size: 24px;
        }

        h2 {
            color: #34495e;
            margin-bottom: 20px;
            font-size: 18px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .status-section {
            margin-bottom: 30px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .status-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .status-value {
            font-family: 'Courier New', monospace;
            padding: 4px 8px;
            background: #ecf0f1;
            border-radius: 4px;
            color: #2c3e50;
        }

        .status-connected {
            border-left-color: #27ae60;
        }

        .status-disconnected {
            border-left-color: #e74c3c;
        }

        .control-section {
            margin-bottom: 30px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-connect {
            background: #27ae60;
            color: white;
        }

        .btn-connect:hover {
            background: #229954;
        }

        .btn-disconnect {
            background: #e74c3c;
            color: white;
        }

        .btn-disconnect:hover {
            background: #c0392b;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        input[type="number"] {
            flex: 1;
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
        }

        input[type="number"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .messages-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .message-count {
            color: #7f8c8d;
            font-size: 14px;
        }

        .messages-list {
            flex: 1;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            overflow-y: auto;
            max-height: 500px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #bdc3c7;
        }

        .message-sent {
            background: #e8f5e8;
            border-left-color: #27ae60;
        }

        .message-received {
            background: #e8f4fd;
            border-left-color: #3498db;
        }

        .message-error {
            background: #fdf2f2;
            border-left-color: #e74c3c;
        }

        .message-time {
            color: #7f8c8d;
            font-size: 11px;
            margin-bottom: 5px;
        }

        .message-content {
            color: #2c3e50;
            word-break: break-all;
        }

        .clear-btn {
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .clear-btn:hover {
            background: #c0392b;
        }

        .server-input {
            margin-bottom: 20px;
        }

        .server-input input {
            width: 100%;
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
        }

        .server-input label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        /* 滑块样式 */
        .slider-container {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .slider-label {
            display: block;
            margin-bottom: 15px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            font-size: 16px;
        }

        .delay-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 10px;
        }

        .delay-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .delay-slider::-webkit-slider-thumb:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .delay-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .slider-range {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .quick-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-quick {
            padding: 8px 12px;
            background: #ecf0f1;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-quick:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .audio-info {
            margin-top: 15px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .audio-info .status-item {
            margin-bottom: 8px;
            padding: 8px;
            background: #f8f9fa;
        }

        .audio-info .status-item:last-child {
            margin-bottom: 0;
        }

        /* 延时监控样式 */
        .delay-monitor-container {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
        }

        .delay-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .delay-metric {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            text-align: center;
            min-height: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .delay-metric.signal-good {
            border-left-color: #28a745;
        }

        .delay-metric.signal-weak {
            border-left-color: #ffc107;
        }

        .delay-metric.signal-bad {
            border-left-color: #dc3545;
        }

        .metric-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 3px;
            font-weight: 500;
        }

        .metric-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }

        .metric-unit {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }

        .delay-chart-container {
            height: 200px;
            margin: 15px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .delay-chart-container canvas {
            width: 100%;
            height: 100%;
        }

        /* AEC 文件处理样式 */
        .file-upload {
            margin: 15px 0;
        }

        .upload-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #f9f9f9;
        }

        .upload-item label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .upload-item input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
        }

        .file-name {
            display: block;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
            font-style: italic;
        }

        .aec-progress, .upload-progress {
            margin: 15px 0;
            padding: 15px;
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            border-radius: 6px;
        }

        .upload-progress {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }

        .upload-progress .progress-fill {
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            color: #495057;
        }

        .progress-file-info {
            margin-bottom: 8px;
            font-size: 12px;
            color: #6c757d;
            font-style: italic;
        }

        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #333;
        }

        /* 音频播放器样式 */
        .audio-player {
            margin: 15px 0;
        }

        .player-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #f9f9f9;
        }

        .player-item label {
            display: block;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .player-item audio {
            width: 100%;
            margin-bottom: 10px;
        }

        .player-item button {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-success:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .btn-danger:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧控制面板 -->
        <div class="left-panel">
            <h1>🎛️ AEC 控制面板</h1>
            
            <!-- 服务器地址 -->
            <div class="server-input">
                <label for="serverUrl">WebSocket 服务器地址:</label>
                <input type="text" id="serverUrl" value="ws://************:9000" placeholder="ws://host:port">
            </div>

            <!-- 连接状态 -->
            <div class="status-section">
                <h2>📊 连接状态</h2>
                <div class="status-item" id="connectionStatus">
                    <span class="status-label">连接状态:</span>
                    <span class="status-value" id="statusText">未连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前延时:</span>
                    <span class="status-value" id="currentDelay">-- ms</span>
                </div>
                <div class="status-item">
                    <span class="status-label">消息计数:</span>
                    <span class="status-value" id="messageCount">发送: 0 | 接收: 0</span>
                </div>
            </div>

            <!-- 连接控制 -->
            <div class="control-section">
                <h2>🔌 连接控制</h2>
                <div class="button-group">
                    <button class="btn-connect" onclick="connectWebSocket()">连接</button>
                    <button class="btn-disconnect" onclick="disconnectWebSocket()">断开</button>
                </div>
            </div>

            <!-- 音频播放控制 -->
            <div class="control-section">
                <h2>🎵 音频播放控制</h2>
                <div class="button-group">
                    <button class="btn-primary" id="startAudioBtn" onclick="startAudioStream()">开始播放</button>
                    <button class="btn-secondary" id="stopAudioBtn" onclick="stopAudioStream()" disabled>停止播放</button>
                </div>
                <div class="audio-info">
                    <div class="status-item">
                        <span class="status-label">播放状态:</span>
                        <span class="status-value" id="audioPlayStatus">未播放</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">音频格式:</span>
                        <span class="status-value" id="audioFormat">-- Hz, -- ch</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">缓存状态:</span>
                        <span class="status-value" id="bufferStatus">缓存: 0</span>
                    </div>
                </div>
            </div>

            <!-- AEC 控制 -->
            <div class="control-section">
                <h2>🎚️ AEC 延时控制</h2>
                <div class="button-group">
                    <button class="btn-primary" onclick="getAecDelay()">获取延时</button>
                    <button class="btn-secondary" onclick="getAudioStatus()">获取状态</button>
                </div>

                <!-- 滑块控制 -->
                <div class="slider-container">
                    <label for="delaySlider" class="slider-label">
                        延时设置: <span id="sliderValue">55</span> ms
                    </label>
                    <input type="range" id="delaySlider" class="delay-slider"
                           min="1" max="200" value="55" step="1"
                           oninput="updateSliderValue()" onchange="setAecDelayFromSlider()">
                    <div class="slider-range">
                        <span>1ms</span>
                        <span>200ms</span>
                    </div>
                </div>

                <!-- 快速设置按钮 -->
                <div class="quick-buttons">
                    <button class="btn-quick" onclick="setQuickDelay(20)">20ms</button>
                    <button class="btn-quick" onclick="setQuickDelay(40)">40ms</button>
                    <button class="btn-quick" onclick="setQuickDelay(60)">60ms</button>
                    <button class="btn-quick" onclick="setQuickDelay(100)">100ms</button>
                </div>
            </div>

            <!-- AEC 文件处理 -->
            <div class="control-section">
                <h2>🎯 AEC 文件处理</h2>
                <div class="file-upload">
                    <div class="upload-item">
                        <label for="micFile">麦克风文件 (PCM):</label>
                        <input type="file" id="micFile" accept=".pcm,.raw" onchange="handleFileSelect('mic')">
                        <span id="micFileName" class="file-name">未选择文件</span>
                    </div>
                    <div class="upload-item">
                        <label for="refFile">参考信号文件 (PCM):</label>
                        <input type="file" id="refFile" accept=".pcm,.raw" onchange="handleFileSelect('ref')">
                        <span id="refFileName" class="file-name">未选择文件</span>
                    </div>
                </div>
                <div class="button-group">
                    <button class="btn-primary" id="uploadBtn" onclick="uploadFiles()" disabled>上传文件</button>
                    <button class="btn-success" id="processBtn" onclick="startAecProcessing()" disabled>开始处理</button>
                    <button class="btn-danger" id="stopProcessBtn" onclick="stopAecProcessing()" disabled>停止处理</button>
                </div>

                <!-- 上传进度显示 -->
                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress-info">
                        <span id="uploadProgressText">准备上传...</span>
                        <span id="uploadProgressStats"></span>
                    </div>
                    <div class="progress-file-info" id="uploadFileInfo">
                        <span id="uploadFileName"></span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="uploadProgressFill"></div>
                    </div>
                </div>
                <div class="aec-progress" id="aecProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progressText">处理中... 0%</span>
                        <span id="progressTime">0.0s</span>
                    </div>
                </div>
            </div>

            <!-- 音频试听 -->
            <div class="control-section">
                <h2>🎧 音频试听</h2>
                <div class="audio-player">
                    <div class="player-item">
                        <label>麦克风原始音频:</label>
                        <audio id="micAudio" controls style="width: 100%;">
                            您的浏览器不支持音频播放
                        </audio>
                        <button class="btn-secondary" onclick="playUploadedFile('mic')">播放上传文件</button>
                    </div>
                    <div class="player-item">
                        <label>参考信号音频:</label>
                        <audio id="refAudio" controls style="width: 100%;">
                            您的浏览器不支持音频播放
                        </audio>
                        <button class="btn-secondary" onclick="playUploadedFile('ref')">播放上传文件</button>
                    </div>
                    <div class="player-item">
                        <label>回声消除后音频:</label>
                        <audio id="outputAudio" controls style="width: 100%;">
                            您的浏览器不支持音频播放
                        </audio>
                        <button class="btn-success" onclick="downloadOutput()" disabled id="downloadBtn">下载结果</button>
                    </div>
                    <div class="player-item">
                        <label>语音缓冲区数据:</label>
                        <div style="margin: 10px 0;">
                            <button class="btn-primary" onclick="downloadVoiceBuffer()">下载语音缓冲区 (PCM)</button>
                            <span id="voiceBufferInfo" style="margin-left: 10px; color: #666; font-size: 12px;">点击下载当前录音缓冲区数据</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧消息面板 -->
        <div class="right-panel">
            <div class="messages-container">
                <div class="messages-header">
                    <h2>📝 消息记录</h2>
                    <div>
                        <span class="message-count" id="totalMessages">总计: 0 条消息</span>
                        <button class="clear-btn" onclick="clearMessages()">清空</button>
                    </div>
                </div>
                <div class="messages-list" id="messagesList">
                    <div class="message message-received">
                        <div class="message-time">系统消息</div>
                        <div class="message-content">欢迎使用 AEC WebSocket 控制面板！<br>点击左侧"连接"按钮开始。</div>
                    </div>
                </div>
            </div>

            <!-- 延时监控面板 -->
            <div class="delay-monitor-container" style="margin-top: 20px;">
                <div class="messages-header">
                    <h2>🎯 延时监控</h2>
                    <div>
                        <button class="clear-btn" onclick="clearDelayData()">清空数据</button>
                    </div>
                </div>

                <!-- 实时指标 -->
                <div class="delay-metrics">
                    <div class="delay-metric" id="estimatedDelayMetric">
                        <div class="metric-label">估计延时</div>
                        <div class="metric-value" id="estimatedDelay">--</div>
                        <div class="metric-unit">ms</div>
                    </div>
                    <div class="delay-metric" id="currentDelayMetric">
                        <div class="metric-label">当前设置</div>
                        <div class="metric-value" id="currentDelay">--</div>
                        <div class="metric-unit">ms</div>
                    </div>
                    <div class="delay-metric" id="correlationMetric">
                        <div class="metric-label">相关性</div>
                        <div class="metric-value" id="correlation">--</div>
                        <div class="metric-unit"></div>
                    </div>
                    <div class="delay-metric" id="micRmsMetric">
                        <div class="metric-label">麦克风RMS</div>
                        <div class="metric-value" id="micRms">--</div>
                        <div class="metric-unit"></div>
                    </div>
                    <div class="delay-metric" id="refRmsMetric">
                        <div class="metric-label">参考RMS</div>
                        <div class="metric-value" id="refRms">--</div>
                        <div class="metric-unit"></div>
                    </div>
                    <div class="delay-metric" id="signalStatusMetric">
                        <div class="metric-label">信号状态</div>
                        <div class="metric-value" id="signalStatus">--</div>
                        <div class="metric-unit"></div>
                    </div>
                </div>

                <!-- 延时趋势图 -->
                <div class="delay-chart-container">
                    <canvas id="delayChart" width="600" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let sentCount = 0;
        let receivedCount = 0;
        let totalMessages = 1; // 包含欢迎消息

        // 音频播放相关
        let audioContext = null;
        let audioStreamActive = false;
        let currentSampleRate = 16000;
        let currentChannels = 1;

        // 音频缓存相关
        let audioBufferQueue = [];
        let isPlaying = false;
        let nextPlayTime = 0;
        let bufferDuration = 0.1; // 100ms缓冲时间
        let maxBufferSize = 6;    // 最大缓存6个音频块，减少延时
        let masterGainNode = null; // 主音量控制节点

        // 延时监控相关
        let delayData = [];
        let maxDelayDataPoints = 30;
        let delayCanvas = null;
        let delayCtx = null;

        // AEC文件处理相关
        let micFileData = null;
        let refFileData = null;
        let micFileName = '';
        let refFileName = '';
        let outputFileUrl = null;
        let isAecProcessing = false;

        // AEC结果数据接收
        let aecResultChunks = [];
        let expectedChunks = 0;
        let receivedChunks = 0;
        let resultSamples = 0;

        // 分包上传相关
        let chunkSize = 8192; // 8KB per chunk，更小的包提高稳定性
        let micUploadComplete = false;
        let refUploadComplete = false;
        let currentUploadType = null;

        // 分块上传相关
        let isUploading = false;
        let uploadChunkSize = 32768; // 32KB per chunk

        // 上传进度跟踪
        let uploadProgress = {
            currentChunk: 0,
            totalChunks: 0,
            type: '',
            startTime: 0
        };

        // 结果数据接收跟踪
        let resultReceiver = {
            chunks: [],
            totalChunks: 0,
            receivedChunks: 0,
            samples: 0
        };

        // 显示上传进度
        function showUploadProgress(type, currentChunk, totalChunks) {
            const progressDiv = document.getElementById('uploadProgress');
            const progressText = document.getElementById('uploadProgressText');
            const progressStats = document.getElementById('uploadProgressStats');
            const progressFill = document.getElementById('uploadProgressFill');
            const fileInfo = document.getElementById('uploadFileName');

            if (currentChunk === 0) {
                // 开始上传
                uploadProgress.startTime = Date.now();
                progressDiv.style.display = 'block';

                // 显示文件信息
                const fileName = type === 'mic' ? micFileName : refFileName;
                const fileSize = type === 'mic' ? micFileData.length : refFileData.length;
                const fileSizeKB = (fileSize / 1024).toFixed(1);
                const chunkSizeKB = (chunkSize / 1024).toFixed(1);
                fileInfo.textContent = `文件: ${fileName} (${fileSizeKB} KB, PCM格式) | 块大小: ${chunkSizeKB} KB`;
            }

            uploadProgress.currentChunk = currentChunk;
            uploadProgress.totalChunks = totalChunks;
            uploadProgress.type = type;

            const percentage = totalChunks > 0 ? (currentChunk / totalChunks) * 100 : 0;
            const typeName = type === 'mic' ? '麦克风' : '参考信号';

            progressText.textContent = `正在上传${typeName}数据...`;
            progressStats.textContent = `${currentChunk}/${totalChunks} 块 (${percentage.toFixed(1)}%)`;
            progressFill.style.width = `${percentage}%`;

            // 计算速度和剩余时间
            if (currentChunk > 0) {
                const elapsed = (Date.now() - uploadProgress.startTime) / 1000;
                const speed = currentChunk / elapsed;
                const remaining = totalChunks - currentChunk;
                const eta = remaining / speed;

                if (eta > 0 && eta < 3600) { // 小于1小时才显示
                    progressStats.textContent += ` | 预计剩余: ${eta.toFixed(0)}秒`;
                }
            }

            // 上传完成
            if (currentChunk >= totalChunks) {
                progressText.textContent = `${typeName}数据上传完成`;
                progressStats.textContent = `${totalChunks}/${totalChunks} 块 (100.0%) | 上传完成`;
                // 不在这里隐藏进度，让handleUploadComplete来控制
            }
        }

        // 隐藏上传进度
        function hideUploadProgress() {
            const progressDiv = document.getElementById('uploadProgress');
            progressDiv.style.display = 'none';
        }

        function updateConnectionStatus(status, className) {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            statusElement.className = `status-item ${className}`;
            statusText.textContent = status;
        }

        function updateMessageCount() {
            document.getElementById('messageCount').textContent = `发送: ${sentCount} | 接收: ${receivedCount}`;
            document.getElementById('totalMessages').textContent = `总计: ${totalMessages} 条消息`;
        }

        function addMessage(content, type = 'received', isJson = false) {
            const messagesList = document.getElementById('messagesList');
            const messageDiv = document.createElement('div');
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            
            let displayContent = content;
            if (isJson) {
                try {
                    const jsonObj = JSON.parse(content);
                    displayContent = JSON.stringify(jsonObj, null, 2);
                } catch (e) {
                    displayContent = content;
                }
            }
            
            messageDiv.className = `message message-${type}`;
            messageDiv.innerHTML = `
                <div class="message-time">${timeStr} - ${type === 'sent' ? '发送' : type === 'error' ? '错误' : '接收'}</div>
                <div class="message-content">${displayContent}</div>
            `;
            
            messagesList.appendChild(messageDiv);
            messagesList.scrollTop = messagesList.scrollHeight;
            
            totalMessages++;
            if (type === 'sent') sentCount++;
            else if (type === 'received') receivedCount++;
            
            updateMessageCount();
        }

        function connectWebSocket() {
            const serverUrl = document.getElementById('serverUrl').value;

            // 保存到localStorage
            saveWebSocketUrl(serverUrl);

            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('WebSocket 已经连接', 'error');
                return;
            }

            try {
                updateConnectionStatus('连接中...', 'status-disconnected');
                addMessage(`正在连接到: ${serverUrl}`, 'sent');

                ws = new WebSocket(serverUrl, 'aec-control-protocol');

                ws.onopen = function(event) {
                    updateConnectionStatus('已连接', 'status-connected');
                    addMessage('WebSocket 连接成功！', 'received');
                };
                
                ws.onmessage = function(event) {
                    // 尝试解析消息
                    try {
                        const data = JSON.parse(event.data);

                        // 处理音频数据（不显示在消息记录中，避免刷屏）
                        if (data.type === 'audio_data') {
                            handleAudioData(data);
                            return; // 不添加到消息记录
                        }

                        // 处理延时监控数据（不显示在消息记录中，避免刷屏）
                        if (data.type === 'delay_estimate_notify') {
                            handleDelayEstimate(data);
                            return; // 不添加到消息记录
                        }

                        if (data.type === 'signal_strength_notify') {
                            handleSignalStrength(data);
                            return; // 不添加到消息记录
                        }

                        // 其他消息正常显示
                        addMessage(event.data, 'received', true);

                        // 处理不同类型的消息
                        if (data.type === 'aec_delay_notify' || data.type === 'aec_delay_response') {
                            let currentDelayMs = null;

                            if (data.new_delay_ms !== undefined) {
                                currentDelayMs = data.new_delay_ms;
                            } else if (data.delay_ms !== undefined) {
                                currentDelayMs = data.delay_ms;
                            }

                            if (currentDelayMs !== null) {
                                // 更新状态显示
                                document.getElementById('currentDelay').textContent = currentDelayMs + ' ms';

                                // 同步更新滑块位置
                                const slider = document.getElementById('delaySlider');
                                const valueDisplay = document.getElementById('sliderValue');
                                slider.value = currentDelayMs;
                                valueDisplay.textContent = currentDelayMs;
                            }
                        } else if (data.type === 'audio_stream_response') {
                            updateAudioStreamStatus(data.status);
                        } else if (data.type === 'start_upload_response') {
                            handleStartUploadResponse(data);
                        } else if (data.type === 'chunk_response') {
                            handleChunkResponse(data);
                        } else if (data.type === 'upload_complete') {
                            handleUploadComplete(data);
                        } else if (data.type === 'aec_response') {
                            handleAecResponse(data);
                        } else if (data.type === 'aec_progress') {
                            handleAecProgress(data);
                        } else if (data.type === 'aec_complete') {
                            handleAecComplete(data);
                        } else if (data.type === 'aec_data_chunk') {
                            handleAecDataChunk(data);
                        } else if (data.type === 'aec_result_response') {
                            handleAecResultResponse(data);
                        } else if (data.type === 'aec_result_chunk') {
                            handleAecResultChunk(data);
                        } else if (data.type === 'aec_result_complete') {
                            handleAecResultComplete(data);
                        } else if (data.type === 'aec_status_response') {
                            handleAecStatusResponse(data);
                        }
                    } catch (e) {
                        // 如果不是JSON，正常显示
                        addMessage(event.data, 'received');
                    }
                };
                
                ws.onclose = function(event) {
                    updateConnectionStatus('已断开', 'status-disconnected');
                    addMessage(`WebSocket 连接关闭 (代码: ${event.code})`, 'error');
                };
                
                ws.onerror = function(error) {
                    updateConnectionStatus('连接错误', 'status-disconnected');
                    addMessage('WebSocket 连接错误', 'error');
                };
                
            } catch (error) {
                updateConnectionStatus('连接失败', 'status-disconnected');
                addMessage(`连接失败: ${error.message}`, 'error');
            }
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                updateConnectionStatus('已断开', 'status-disconnected');
                addMessage('主动断开 WebSocket 连接', 'sent');
            }
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket 未连接，无法发送消息', 'error');
                return false;
            }
            
            try {
                ws.send(message);
                addMessage(message, 'sent', true);
                return true;
            } catch (error) {
                addMessage(`发送消息失败: ${error.message}`, 'error');
                return false;
            }
        }

        function getAecDelay() {
            const message = JSON.stringify({type: "get_aec_delay"});
            sendMessage(message);
        }

        function updateSliderValue() {
            const slider = document.getElementById('delaySlider');
            const valueDisplay = document.getElementById('sliderValue');
            valueDisplay.textContent = slider.value;
        }

        function setAecDelayFromSlider() {
            const delayValue = parseInt(document.getElementById('delaySlider').value);
            setAecDelayValue(delayValue);
        }

        function setQuickDelay(delayMs) {
            const slider = document.getElementById('delaySlider');
            const valueDisplay = document.getElementById('sliderValue');

            slider.value = delayMs;
            valueDisplay.textContent = delayMs;
            setAecDelayValue(delayMs);
        }

        function setAecDelayValue(delayValue) {
            if (isNaN(delayValue) || delayValue < 1 || delayValue > 200) {
                addMessage('延时值必须在 1-200ms 之间', 'error');
                return;
            }

            const message = JSON.stringify({
                type: "set_aec_delay",
                delay_ms: delayValue
            });
            sendMessage(message);
        }

        function getAudioStatus() {
            const message = JSON.stringify({type: "get_audio_status"});
            sendMessage(message);
        }

        // 音频播放相关函数
        async function initAudioContext() {
            if (!audioContext) {
                try {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    if (audioContext.state === 'suspended') {
                        await audioContext.resume();
                    }

                    // 创建主增益节点用于音量控制和平滑过渡
                    masterGainNode = audioContext.createGain();
                    masterGainNode.connect(audioContext.destination);
                    masterGainNode.gain.value = 1.0; // 默认音量

                    console.log('🎵 音频上下文初始化成功，采样率:', audioContext.sampleRate);
                    return true;
                } catch (error) {
                    addMessage(`音频上下文初始化失败: ${error.message}`, 'error');
                    return false;
                }
            }
            return true;
        }

        async function startAudioStream() {
            console.log('🎵 Starting audio stream...');
            addMessage('用户点击了"开始播放"按钮', 'sent');

            if (!await initAudioContext()) {
                addMessage('音频上下文初始化失败', 'error');
                return;
            }

            const message = JSON.stringify({type: "start_audio_stream"});
            console.log('📤 Sending start_audio_stream message:', message);

            if (sendMessage(message)) {
                audioStreamActive = true;
                updateAudioStreamButtons();
                document.getElementById('audioPlayStatus').textContent = '正在播放';
                addMessage('音频流启动请求已发送', 'sent');
            } else {
                addMessage('音频流启动请求发送失败', 'error');
            }
        }

        function stopAudioStream() {
            const message = JSON.stringify({type: "stop_audio_stream"});
            if (sendMessage(message)) {
                audioStreamActive = false;
                stopBufferedPlayback(); // 停止缓存播放
                updateAudioStreamButtons();
                document.getElementById('audioPlayStatus').textContent = '已停止';
                addMessage('音频流已停止', 'sent');
            }
        }

        function updateAudioStreamButtons() {
            const startBtn = document.getElementById('startAudioBtn');
            const stopBtn = document.getElementById('stopAudioBtn');

            if (audioStreamActive) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        function updateAudioStreamStatus(status) {
            if (status === 'started') {
                document.getElementById('audioPlayStatus').textContent = '正在播放';
            } else if (status === 'stopped') {
                document.getElementById('audioPlayStatus').textContent = '已停止';
                audioStreamActive = false;
                stopBufferedPlayback(); // 停止缓存播放
                updateAudioStreamButtons();
            }
        }

        function handleAudioData(data) {
            if (!audioContext || !audioStreamActive) return;

            try {
                // 更新音频格式显示
                currentSampleRate = data.sample_rate || 16000;
                currentChannels = data.channels || 1;
                document.getElementById('audioFormat').textContent =
                    `${currentSampleRate} Hz, ${currentChannels} ch`;

                // 解码Base64音频数据
                const binaryString = atob(data.data);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                // 转换为16位PCM
                const pcmData = new Int16Array(bytes.buffer);

                // 创建AudioBuffer
                const audioBuffer = audioContext.createBuffer(
                    currentChannels,
                    pcmData.length / currentChannels,
                    currentSampleRate
                );

                // 填充音频数据
                for (let channel = 0; channel < currentChannels; channel++) {
                    const channelData = audioBuffer.getChannelData(channel);
                    for (let i = 0; i < channelData.length; i++) {
                        if (currentChannels === 1) {
                            channelData[i] = pcmData[i] / 32768.0; // 转换为-1到1的范围
                        } else {
                            channelData[i] = pcmData[i * currentChannels + channel] / 32768.0;
                        }
                    }
                }

                // 添加到缓存队列
                addToAudioQueue(audioBuffer);

            } catch (error) {
                console.error('音频处理错误:', error);
            }
        }

        function addToAudioQueue(audioBuffer) {
            // 防止缓存队列过大，但给一些余量
            if (audioBufferQueue.length >= maxBufferSize) {
                // 移除多个旧的音频块，避免频繁丢弃
                const removeCount = Math.min(2, audioBufferQueue.length - 3);
                for (let i = 0; i < removeCount; i++) {
                    audioBufferQueue.shift();
                }
                if (removeCount > 0) {
                    console.warn(`音频缓存队列满，丢弃 ${removeCount} 个旧数据块`);
                }
            }

            audioBufferQueue.push(audioBuffer);

            // 更新缓存状态显示
            updateBufferStatusDisplay();

            // 如果还没开始播放，启动播放
            if (!isPlaying && audioBufferQueue.length >= 2) { // 等待2个缓冲块再开始播放，减少延时
                startBufferedPlayback();
            }
        }

        function startBufferedPlayback() {
            if (!audioContext || audioBufferQueue.length === 0) return;

            isPlaying = true;
            // 更短的初始延迟，但确保音频上下文稳定
            nextPlayTime = audioContext.currentTime + 0.02; // 20ms后开始播放

            console.log('🎵 开始缓存播放，队列长度:', audioBufferQueue.length, '初始播放时间:', nextPlayTime.toFixed(3));
            scheduleNextAudioBuffer();
        }

        function scheduleNextAudioBuffer() {
            if (!isPlaying || !audioStreamActive || !audioContext || !masterGainNode) {
                isPlaying = false;
                return;
            }

            if (audioBufferQueue.length === 0) {
                // 缓存为空，等待新数据
                setTimeout(() => {
                    if (audioBufferQueue.length > 0 && isPlaying) {
                        scheduleNextAudioBuffer();
                    } else {
                        console.log('🔇 音频缓存为空，暂停播放');
                        isPlaying = false;
                    }
                }, 20); // 减少到20ms重试间隔
                return;
            }

            try {

            const audioBuffer = audioBufferQueue.shift();
            const source = audioContext.createBufferSource();
            source.buffer = audioBuffer;

            // 计算播放时间和持续时间（必须先计算）
            const bufferDuration = audioBuffer.length / audioBuffer.sampleRate;
            const currentTime = audioContext.currentTime;

            // 创建增益节点用于平滑过渡
            const gainNode = audioContext.createGain();
            source.connect(gainNode);
            gainNode.connect(masterGainNode);

            // 添加轻微的淡入淡出效果，减少咯咯声
            const fadeTime = 0.002; // 2ms的淡入淡出
            gainNode.gain.setValueAtTime(0, nextPlayTime);
            gainNode.gain.linearRampToValueAtTime(1, nextPlayTime + fadeTime);
            gainNode.gain.setValueAtTime(1, nextPlayTime + bufferDuration - fadeTime);
            gainNode.gain.linearRampToValueAtTime(0, nextPlayTime + bufferDuration);

            // 更精确的时间同步
            if (nextPlayTime < currentTime + 0.01) { // 如果计划时间太接近当前时间
                nextPlayTime = currentTime + 0.01; // 给一点缓冲时间
            }

            source.start(nextPlayTime);

            // 提前调度下一个音频块，不等待onended事件
            const scheduleDelay = Math.max(10, (bufferDuration * 1000) - 50); // 提前50ms调度
            setTimeout(() => {
                scheduleNextAudioBuffer();
            }, scheduleDelay);

            // 更新下次播放时间
            nextPlayTime += bufferDuration;

            // 显示缓存状态（降低频率）
            if (Math.random() < 0.02) { // 约2%的概率打印状态
                console.log(`🎵 播放中，队列: ${audioBufferQueue.length}, 当前时间: ${currentTime.toFixed(3)}, 下次: ${nextPlayTime.toFixed(3)}`);
            }

            // 更新界面缓存状态
            updateBufferStatusDisplay();

            } catch (error) {
                console.error('音频播放调度错误:', error);
                // 发生错误时，稍后重试
                setTimeout(() => {
                    if (isPlaying && audioBufferQueue.length > 0) {
                        scheduleNextAudioBuffer();
                    }
                }, 100);
            }
        }

        function updateBufferStatusDisplay() {
            const bufferStatusElement = document.getElementById('bufferStatus');
            if (!bufferStatusElement) return;

            bufferStatusElement.textContent = `缓存: ${audioBufferQueue.length}`;

            // 根据缓存状态调整颜色
            if (audioBufferQueue.length < 1) {
                bufferStatusElement.style.color = '#e74c3c'; // 红色：缓存不足
            } else if (audioBufferQueue.length < 3) {
                bufferStatusElement.style.color = '#f39c12'; // 橙色：缓存较低
            } else {
                bufferStatusElement.style.color = '#27ae60'; // 绿色：缓存良好
            }
        }

        function stopBufferedPlayback() {
            isPlaying = false;
            audioBufferQueue = [];
            nextPlayTime = 0;
            console.log('🛑 停止缓存播放');
        }

        function clearMessages() {
            const messagesList = document.getElementById('messagesList');
            messagesList.innerHTML = '';
            sentCount = 0;
            receivedCount = 0;
            totalMessages = 0;
            updateMessageCount();
            addMessage('消息记录已清空', 'received');
        }

        // WebSocket地址管理
        function loadLastWebSocketUrl() {
            try {
                const lastUrl = localStorage.getItem('lastWebSocketUrl');
                if (lastUrl) {
                    document.getElementById('serverUrl').value = lastUrl;
                    console.log('从localStorage加载WebSocket地址:', lastUrl);
                    addMessage(`已加载上次连接地址: ${lastUrl}`, 'received');
                } else {
                    console.log('localStorage中没有保存的WebSocket地址');
                }
            } catch (error) {
                console.error('加载WebSocket地址失败:', error);
            }
        }

        function saveWebSocketUrl(url) {
            try {
                if (url && url.trim()) {
                    localStorage.setItem('lastWebSocketUrl', url.trim());
                    console.log('WebSocket地址已保存到localStorage:', url);
                }
            } catch (error) {
                console.error('保存WebSocket地址失败:', error);
            }
        }

        // 延时监控相关函数
        function initDelayChart() {
            delayCanvas = document.getElementById('delayChart');
            if (delayCanvas) {
                delayCtx = delayCanvas.getContext('2d');
                drawDelayChart();
            }
        }

        function drawDelayChart() {
            if (!delayCtx || !delayCanvas) return;

            delayCtx.clearRect(0, 0, delayCanvas.width, delayCanvas.height);

            if (delayData.length === 0) {
                delayCtx.fillStyle = '#666';
                delayCtx.font = '14px Arial';
                delayCtx.textAlign = 'center';
                delayCtx.fillText('等待延时数据...', delayCanvas.width / 2, delayCanvas.height / 2);
                return;
            }

            // 绘制网格
            delayCtx.strokeStyle = '#e0e0e0';
            delayCtx.lineWidth = 1;

            // 垂直网格线
            for (let i = 0; i <= 6; i++) {
                let x = (delayCanvas.width / 6) * i;
                delayCtx.beginPath();
                delayCtx.moveTo(x, 0);
                delayCtx.lineTo(x, delayCanvas.height);
                delayCtx.stroke();
            }

            // 水平网格线
            for (let i = 0; i <= 4; i++) {
                let y = (delayCanvas.height / 4) * i;
                delayCtx.beginPath();
                delayCtx.moveTo(0, y);
                delayCtx.lineTo(delayCanvas.width, y);
                delayCtx.stroke();
            }

            // 绘制延时数据
            if (delayData.length > 1) {
                delayCtx.strokeStyle = '#007bff';
                delayCtx.lineWidth = 2;
                delayCtx.beginPath();

                let minDelay = Math.min(...delayData.map(d => d.estimated));
                let maxDelay = Math.max(...delayData.map(d => d.estimated));
                let range = maxDelay - minDelay || 1;

                delayData.forEach((point, index) => {
                    let x = (delayCanvas.width / (delayData.length - 1)) * index;
                    let y = delayCanvas.height - ((point.estimated - minDelay) / range) * delayCanvas.height;

                    if (index === 0) {
                        delayCtx.moveTo(x, y);
                    } else {
                        delayCtx.lineTo(x, y);
                    }
                });

                delayCtx.stroke();

                // 绘制当前设置线
                if (delayData.length > 0) {
                    let currentDelay = delayData[delayData.length - 1].current;
                    let y = delayCanvas.height - ((currentDelay - minDelay) / range) * delayCanvas.height;

                    delayCtx.strokeStyle = '#dc3545';
                    delayCtx.lineWidth = 1;
                    delayCtx.setLineDash([5, 5]);
                    delayCtx.beginPath();
                    delayCtx.moveTo(0, y);
                    delayCtx.lineTo(delayCanvas.width, y);
                    delayCtx.stroke();
                    delayCtx.setLineDash([]);
                }
            }
        }

        function handleDelayEstimate(data) {
            // 更新指标显示
            document.getElementById('estimatedDelay').textContent = data.estimated_delay_ms.toFixed(1);
            document.getElementById('currentDelay').textContent = data.current_delay_ms;
            document.getElementById('correlation').textContent = data.correlation.toFixed(3);
            document.getElementById('micRms').textContent = data.mic_rms.toFixed(1);
            document.getElementById('refRms').textContent = data.ref_rms.toFixed(1);

            // 添加到图表数据
            delayData.push({
                estimated: data.estimated_delay_ms,
                current: data.current_delay_ms,
                timestamp: data.timestamp
            });

            if (delayData.length > maxDelayDataPoints) {
                delayData.shift();
            }

            drawDelayChart();

            // 更新延时指标颜色
            let delayDiff = Math.abs(data.estimated_delay_ms - data.current_delay_ms);
            let delayMetric = document.getElementById('estimatedDelayMetric');
            if (delayDiff < 10) {
                delayMetric.className = 'delay-metric signal-good';
            } else if (delayDiff < 20) {
                delayMetric.className = 'delay-metric signal-weak';
            } else {
                delayMetric.className = 'delay-metric signal-bad';
            }
        }

        function handleSignalStrength(data) {
            document.getElementById('micRms').textContent = data.mic_rms.toFixed(1);
            document.getElementById('refRms').textContent = data.ref_rms.toFixed(1);
            document.getElementById('signalStatus').textContent = data.status === 'good' ? '良好' : '较弱';

            // 更新信号状态颜色
            let signalMetric = document.getElementById('signalStatusMetric');
            signalMetric.className = data.status === 'good' ? 'delay-metric signal-good' : 'delay-metric signal-weak';
        }

        function clearDelayData() {
            delayData = [];
            drawDelayChart();

            // 重置所有指标显示
            document.getElementById('estimatedDelay').textContent = '--';
            document.getElementById('currentDelay').textContent = '--';
            document.getElementById('correlation').textContent = '--';
            document.getElementById('micRms').textContent = '--';
            document.getElementById('refRms').textContent = '--';
            document.getElementById('signalStatus').textContent = '--';

            // 重置指标颜色
            document.getElementById('estimatedDelayMetric').className = 'delay-metric';
            document.getElementById('signalStatusMetric').className = 'delay-metric';

            addMessage('延时监控数据已清空', 'received');
        }

        // AEC文件处理相关函数
        function handleFileSelect(type) {
            const fileInput = document.getElementById(type + 'File');
            const fileName = document.getElementById(type + 'FileName');
            const file = fileInput.files[0];

            if (file) {
                fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';

                // 保存文件信息
                if (type === 'mic') {
                    micFileData = file;
                    micFileName = file.name;
                } else if (type === 'ref') {
                    refFileData = file;
                    refFileName = file.name;
                }

                // 检查是否可以启用上传按钮
                updateUploadButton();

                // 为试听创建音频URL
                const reader = new FileReader();
                reader.onload = function(e) {
                    createAudioUrl(e.target.result, type);
                };
                reader.readAsArrayBuffer(file);
            } else {
                fileName.textContent = '未选择文件';
                if (type === 'mic') {
                    micFileData = null;
                    micFileName = '';
                } else if (type === 'ref') {
                    refFileData = null;
                    refFileName = '';
                }
                updateUploadButton();
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function arrayBufferToBase64(buffer) {
            let binary = '';
            const bytes = new Uint8Array(buffer);
            const len = bytes.byteLength;
            for (let i = 0; i < len; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            return window.btoa(binary);
        }

        function createAudioUrl(arrayBuffer, type) {
            // 创建WAV头部（假设48kHz单声道16bit PCM）
            const wavBuffer = createWavFile(arrayBuffer, 48000, 1, 16);
            const blob = new Blob([wavBuffer], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);

            const audioElement = document.getElementById(type + 'Audio');
            audioElement.src = url;

            // 设置默认音量为较低值，避免呼啸声
            audioElement.volume = 0.1;

            // 添加音频加载事件监听
            audioElement.addEventListener('loadedmetadata', function() {
                console.log(`${type} audio loaded: duration=${this.duration}s, volume=${this.volume}`);
            });

            audioElement.addEventListener('error', function(e) {
                console.error(`${type} audio error:`, e);
                addMessage(`❌ ${type === 'mic' ? '麦克风' : '参考信号'}音频加载失败`, 'received');
            });
        }

        function createWavFile(pcmBuffer, sampleRate, channels, bitsPerSample) {
            const pcmLength = pcmBuffer.byteLength;
            const wavLength = 44 + pcmLength;
            const wavBuffer = new ArrayBuffer(wavLength);
            const view = new DataView(wavBuffer);

            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, wavLength - 8, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);  // PCM format
            view.setUint16(22, channels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * channels * bitsPerSample / 8, true);
            view.setUint16(32, channels * bitsPerSample / 8, true);
            view.setUint16(34, bitsPerSample, true);
            writeString(36, 'data');
            view.setUint32(40, pcmLength, true);

            // 处理PCM数据 - 检查并规范化音频数据
            const pcmView = new Uint8Array(pcmBuffer);
            const wavView = new Uint8Array(wavBuffer);

            // 如果是16位PCM数据，检查音量并进行适当的衰减
            if (bitsPerSample === 16 && pcmLength >= 2) {
                const samples = new Int16Array(pcmBuffer);
                let maxSample = 0;

                // 找到最大样本值
                for (let i = 0; i < samples.length; i++) {
                    maxSample = Math.max(maxSample, Math.abs(samples[i]));
                }

                // 如果音频过大，进行衰减
                if (maxSample > 16384) { // 约50%的最大值
                    const scale = 16384 / maxSample;
                    console.log(`Audio too loud (max: ${maxSample}), scaling by ${scale.toFixed(3)}`);

                    for (let i = 0; i < samples.length; i++) {
                        samples[i] = Math.round(samples[i] * scale);
                    }
                }

                // 复制处理后的数据
                const scaledView = new Uint8Array(samples.buffer);
                wavView.set(scaledView, 44);
            } else {
                // 直接复制原始数据
                wavView.set(pcmView, 44);
            }

            return wavBuffer;
        }

        function updateUploadButton() {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = !(micFileData && refFileData);
        }

        function updateAecButtonsAfterUpload() {
            const uploadBtn = document.getElementById('uploadBtn');
            const processBtn = document.getElementById('processBtn');
            const stopProcessBtn = document.getElementById('stopProcessBtn');

            // 检查上传状态
            const canProcess = micUploadComplete && refUploadComplete;

            // 上传成功后，启用处理按钮
            processBtn.disabled = !canProcess || isAecProcessing;
            stopProcessBtn.disabled = !isAecProcessing;
            uploadBtn.disabled = !(micFileData && refFileData);

            // 调试信息
            console.log(`Button states: mic=${micUploadComplete}, ref=${refUploadComplete}, canProcess=${canProcess}, processing=${isAecProcessing}`);

            // 更新按钮文本提示
            if (!canProcess) {
                processBtn.title = micUploadComplete ? '等待参考信号上传完成' : '等待麦克风数据上传完成';
            } else {
                processBtn.title = '开始AEC处理';
            }
        }

        function uploadFiles() {
            if (!micFileData || !refFileData) {
                addMessage('❌ 请先选择麦克风和参考信号文件', 'received');
                return;
            }

            // 禁用上传按钮，显示上传中状态
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';

            // 重置上传状态
            micUploadComplete = false;
            refUploadComplete = false;

            // 更新按钮状态，确保处理按钮被禁用
            updateAecButtonsAfterUpload();

            addMessage('📤 开始HTTP上传音频文件...', 'sent');

            // 使用HTTP上传文件
            uploadFilesViaHTTP();
        }

        // HTTP上传文件
        async function uploadFilesViaHTTP() {
            try {
                // 获取原始文件对象
                const micFile = document.getElementById('micFile').files[0];
                const refFile = document.getElementById('refFile').files[0];

                if (!micFile || !refFile) {
                    addMessage('❌ 文件对象丢失，请重新选择文件', 'received');
                    resetUploadButton();
                    return;
                }

                // 上传麦克风文件
                addMessage('📤 上传麦克风文件...', 'sent');
                const micResult = await uploadSingleFile(micFile, 'mic');
                if (!micResult.success) {
                    addMessage(`❌ 麦克风文件上传失败: ${micResult.error}`, 'received');
                    resetUploadButton();
                    return;
                }

                micUploadComplete = true;
                addMessage('✅ 麦克风文件上传成功', 'received');
                updateAecButtonsAfterUpload();

                // 上传参考信号文件
                addMessage('📤 上传参考信号文件...', 'sent');
                const refResult = await uploadSingleFile(refFile, 'ref');
                if (!refResult.success) {
                    addMessage(`❌ 参考信号文件上传失败: ${refResult.error}`, 'received');
                    resetUploadButton();
                    return;
                }

                refUploadComplete = true;
                addMessage('✅ 参考信号文件上传成功', 'received');
                updateAecButtonsAfterUpload();

                addMessage('🎉 所有文件上传完成，可以开始处理', 'received');
                resetUploadButton();

            } catch (error) {
                addMessage(`❌ 上传过程出错: ${error.message}`, 'received');
                resetUploadButton();
            }
        }

        // 上传单个文件
        async function uploadSingleFile(file, type) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', type);

            // 获取HTTP服务器地址
            const wsUrl = localStorage.getItem('websocketUrl') || 'ws://192.168.6.140:9000';
            const httpUrl = wsUrl.replace('ws://', 'http://').replace(':9000', ':8080');

            try {
                const response = await fetch(`${httpUrl}/upload_audio`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    return { success: true, data: result };
                } else {
                    const errorText = await response.text();
                    return { success: false, error: errorText };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function startChunkedUpload(type, data, samples) {
            currentUploadType = type;
            const totalChunks = Math.ceil(data.length / chunkSize);

            const message = {
                type: `start_${type}_upload`,
                total_chunks: totalChunks,
                total_size: data.length,
                samples: samples
            };

            sendMessage(JSON.stringify(message));
            addMessage(`📦 开始${type === 'mic' ? '麦克风' : '参考信号'}分包上传: ${totalChunks} 个数据包`, 'sent');
            showUploadProgress(type, 0, totalChunks);
        }

        function uploadChunk(type, data, chunkIndex) {
            const start = chunkIndex * chunkSize;
            const end = Math.min(start + chunkSize, data.length);
            const chunkData = data.substring(start, end);

            const message = {
                type: `upload_${type}_chunk`,
                chunk_index: chunkIndex,
                chunk_data: chunkData
            };

            sendMessage(JSON.stringify(message));
        }

        function uploadAllChunks(type, data) {
            const totalChunks = Math.ceil(data.length / chunkSize);

            let chunkIndex = 0;

            function sendNextChunk() {
                if (chunkIndex < totalChunks) {
                    uploadChunk(type, data, chunkIndex);
                    showUploadProgress(type, chunkIndex + 1, totalChunks);
                    chunkIndex++;
                    setTimeout(sendNextChunk, 100); // 100ms间隔，提高速度
                } else {
                    // 上传完成
                    addMessage(`✅ ${type === 'mic' ? '麦克风' : '参考信号'}数据上传完成`, 'received');
                }
            }

            sendNextChunk();
        }

        function startAecProcessing() {
            const message = { type: 'start_aec_processing' };
            sendMessage(JSON.stringify(message));

            isAecProcessing = true;
            updateAecButtons();
            showProgress();
        }

        function stopAecProcessing() {
            const message = { type: 'stop_aec_processing' };
            sendMessage(JSON.stringify(message));

            isAecProcessing = false;
            updateAecButtons();
        }

        function updateAecButtons() {
            document.getElementById('processBtn').disabled = isAecProcessing || !micFileData || !refFileData;
            document.getElementById('stopProcessBtn').disabled = !isAecProcessing;
        }

        function showProgress() {
            document.getElementById('aecProgress').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('aecProgress').style.display = 'none';
        }

        function updateProgress(percentage, seconds) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = `处理中... ${percentage.toFixed(1)}%`;
            document.getElementById('progressTime').textContent = `${seconds.toFixed(1)}s`;
        }

        function playUploadedFile(type) {
            const audioElement = document.getElementById(type + 'Audio');
            audioElement.play();
        }

        function downloadOutput() {
            if (window.outputAudioBlob) {
                const url = URL.createObjectURL(window.outputAudioBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `aec_output_${new Date().getTime()}.wav`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                addMessage('📁 文件下载已开始', 'received');
            } else {
                addMessage('❌ 没有可下载的文件', 'received');
            }
        }

        function downloadVoiceBuffer() {
            // 获取WebSocket URL并转换为HTTP URL
            const wsUrl = localStorage.getItem('websocketUrl') || 'ws://192.168.6.140:9000';
            const httpUrl = wsUrl.replace('ws://', 'http://').replace(':9000', ':8080');
            const downloadUrl = `${httpUrl}/download_voice_buffer`;

            addMessage('📥 正在下载语音缓冲区数据...', 'sent');

            // 使用fetch下载文件
            fetch(downloadUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    // 获取音频格式信息
                    const sampleRate = response.headers.get('X-Audio-Sample-Rate') || '16000';
                    const channels = response.headers.get('X-Audio-Channels') || '1';
                    const samples = response.headers.get('X-Audio-Samples') || '0';

                    // 更新界面信息
                    document.getElementById('voiceBufferInfo').textContent =
                        `${samples} 样本, ${sampleRate}Hz, ${channels}声道`;

                    addMessage(`📊 语音缓冲区: ${samples} 样本, ${sampleRate}Hz, ${channels}声道`, 'received');

                    return response.blob();
                })
                .then(blob => {
                    // 创建下载链接
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `voice_buffer_${new Date().getTime()}.pcm`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    addMessage('✅ 语音缓冲区PCM文件下载完成', 'received');
                })
                .catch(error => {
                    console.error('下载语音缓冲区失败:', error);
                    addMessage(`❌ 下载失败: ${error.message}`, 'received');
                });
        }

        // AEC消息处理函数
        function handleStartUploadResponse(data) {
            if (data.status === 'success') {
                const type = data.upload_type;
                addMessage(`✅ ${type === 'mic' ? '麦克风' : '参考信号'}分包上传初始化成功`, 'received');

                // 开始发送数据块
                if (type === 'mic') {
                    uploadAllChunks('mic', micFileData);
                } else if (type === 'ref') {
                    uploadAllChunks('ref', refFileData);
                }
            } else {
                addMessage(`❌ 分包上传初始化失败: ${data.message}`, 'received');
                resetUploadButton();
            }
        }

        function handleChunkResponse(data) {
            if (data.status === 'success') {
                // 更新进度显示
                showUploadProgress(currentUploadType, data.received, data.total);

                // 减少日志频率，只在每10个块或重要节点显示
                if (data.chunk_index % 20 === 0 || data.received === data.total) {
                    addMessage(`📦 ${currentUploadType === 'mic' ? '麦克风' : '参考信号'}数据块 ${data.received}/${data.total}`, 'received');
                }
            } else {
                addMessage(`❌ 数据块上传失败: ${data.message}`, 'received');
                hideUploadProgress();
            }
        }

        function handleUploadComplete(data) {
            if (data.status === 'success') {
                const type = data.upload_type;
                addMessage(`✅ ${type === 'mic' ? '麦克风' : '参考信号'}数据上传完成 - ${data.samples} 样本`, 'received');

                // 延迟隐藏进度，让用户看到完成状态
                setTimeout(() => {
                    hideUploadProgress();
                }, 1500);

                if (type === 'mic') {
                    micUploadComplete = true;
                    addMessage('📤 开始上传参考信号数据...', 'sent');

                    // 更新按钮状态（此时参考信号还未完成，所以处理按钮仍然禁用）
                    updateAecButtonsAfterUpload();

                    // 开始上传参考信号
                    setTimeout(() => {
                        const refSamples = refFileData.length * 3 / 4 / 2;
                        startChunkedUpload('ref', refFileData, Math.floor(refSamples));
                    }, 2000); // 增加延迟，让用户看到麦克风完成
                } else if (type === 'ref') {
                    refUploadComplete = true;

                    // 恢复上传按钮状态
                    resetUploadButton();

                    addMessage('✅ 所有音频数据上传完成', 'received');

                    // 更新按钮状态
                    updateAecButtonsAfterUpload();

                    addMessage('🎯 现在可以开始AEC处理', 'received');
                }
            } else {
                addMessage(`❌ ${data.upload_type === 'mic' ? '麦克风' : '参考信号'}数据上传失败: ${data.message}`, 'received');
                hideUploadProgress();
                resetUploadButton();
            }
        }

        function resetUploadButton() {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = !(micFileData && refFileData);
            uploadBtn.textContent = '上传文件';
        }

        function handleAecResponse(data) {
            if (data.status === 'started') {
                addMessage(`🚀 AEC处理已开始 - 总帧数: ${data.total_frames}, 麦克风: ${data.mic_samples} 样本, 参考: ${data.ref_samples} 样本`, 'received');
                isAecProcessing = true;
                updateAecButtons();
                showProgress();

                // 重置进度
                updateProgress(0, 0);
            } else if (data.status === 'stopped') {
                addMessage('⏹️ AEC处理已停止', 'received');
                isAecProcessing = false;
                updateAecButtons();
                hideProgress();
            } else if (data.status === 'error') {
                addMessage('❌ AEC处理错误: ' + data.message, 'received');
                isAecProcessing = false;
                updateAecButtons();
                hideProgress();
            }
        }

        function handleAecProgress(data) {
            updateProgress(data.percentage, data.seconds);
        }

        function handleAecDataChunk(data) {
            // 接收AEC处理结果数据块
            if (data.chunk === 0) {
                // 第一个块，初始化接收状态
                aecResultChunks = [];
                expectedChunks = data.total_chunks;
                receivedChunks = 0;
                addMessage(`📦 开始接收处理结果数据 (${data.total_chunks} 块)`, 'received');
            }

            aecResultChunks[data.chunk] = data.data;
            receivedChunks++;

            addMessage(`📦 接收数据块 ${data.chunk + 1}/${data.total_chunks}`, 'received');

            // 检查是否接收完所有块
            if (receivedChunks === expectedChunks) {
                // 合并所有数据块
                let completeData = '';
                for (let i = 0; i < expectedChunks; i++) {
                    completeData += aecResultChunks[i];
                }

                // 创建音频URL用于播放和下载
                createOutputAudioUrl(completeData);
                addMessage('✅ 处理结果数据接收完成', 'received');
            }
        }

        function createOutputAudioUrl(base64Data) {
            try {
                // 解码base64数据
                const binaryString = atob(base64Data);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                // 创建WAV文件
                const wavBuffer = createWavFile(bytes.buffer, 48000, 1, 16);
                const blob = new Blob([wavBuffer], { type: 'audio/wav' });
                outputFileUrl = URL.createObjectURL(blob);

                // 设置音频元素
                const outputAudio = document.getElementById('outputAudio');
                outputAudio.src = outputFileUrl;

                // 启用下载按钮
                document.getElementById('downloadBtn').disabled = false;

                addMessage('🎧 处理结果可供试听和下载', 'received');
            } catch (error) {
                addMessage('❌ 处理结果数据解析失败: ' + error.message, 'received');
            }
        }

        function handleAecComplete(data) {
            isAecProcessing = false;
            updateAecButtons();
            hideProgress();

            if (data.success) {
                addMessage(`✅ ${data.message || 'AEC处理完成'}`, 'received');
                resultSamples = data.samples || 0;

                if (resultSamples > 0) {
                    addMessage(`📊 生成了 ${resultSamples} 个音频样本`, 'received');

                    // 提供下载链接
                    const wsUrl = localStorage.getItem('websocketUrl') || 'ws://192.168.6.140:9000';
                    const httpUrl = wsUrl.replace('ws://', 'http://').replace(':9000', ':8080');
                    const downloadUrl = `${httpUrl}/aec_output.wav`;

                    addMessage(`🎵 处理结果已准备就绪`, 'received');
                    addMessage(`📥 <a href="${downloadUrl}" target="_blank" style="color: #007bff; text-decoration: underline;">点击下载回声消除后的音频</a>`, 'received');

                    // 设置音频播放器
                    const audioElement = document.getElementById('outputAudio');
                    audioElement.src = downloadUrl;

                    // 启用下载按钮
                    const downloadBtn = document.getElementById('downloadBtn');
                    if (downloadBtn) {
                        downloadBtn.disabled = false;
                        downloadBtn.onclick = () => window.open(downloadUrl, '_blank');
                    }
                } else {
                    addMessage('⚠️ 处理完成但无结果数据', 'received');
                }
            } else {
                addMessage(`❌ ${data.message || 'AEC处理失败'}`, 'received');
            }
        }

        // 请求AEC处理结果
        function requestAecResult() {
            const message = {
                type: 'get_aec_result'
            };
            sendMessage(JSON.stringify(message));
        }

        // 处理AEC结果响应
        function handleAecResultResponse(data) {
            if (data.status === 'success' && data.output_data) {
                // 直接数据，不分块
                addMessage('✅ 处理结果获取成功', 'received');

                try {
                    // 解码base64数据
                    const binaryString = atob(data.output_data);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }

                    // 创建音频URL并设置到播放器
                    createOutputAudioUrl(bytes.buffer, data.samples);

                    addMessage('🎵 回声消除后的音频已准备就绪，可以播放', 'received');

                } catch (error) {
                    addMessage('❌ 处理结果数据解析失败: ' + error.message, 'received');
                }
            } else if (data.status === 'chunked') {
                // 分块数据
                addMessage(`📦 开始接收分块结果数据 (${data.total_chunks} 块)`, 'received');

                // 初始化接收器
                resultReceiver.chunks = new Array(data.total_chunks);
                resultReceiver.totalChunks = data.total_chunks;
                resultReceiver.receivedChunks = 0;
                resultReceiver.samples = data.samples;

            } else {
                addMessage(`❌ 获取处理结果失败: ${data.message || '未知错误'}`, 'received');
            }
        }

        // 处理AEC结果分块
        function handleAecResultChunk(data) {
            // 如果是第一个分块，初始化接收器
            if (resultReceiver.totalChunks === 0) {
                resultReceiver.totalChunks = data.total_chunks;
                resultReceiver.chunks = new Array(data.total_chunks);
                addMessage(`📦 开始接收分块结果数据 (${data.total_chunks} 块)`, 'received');
            }

            if (data.chunk >= 0 && data.chunk < resultReceiver.totalChunks) {
                resultReceiver.chunks[data.chunk] = data.data;
                resultReceiver.receivedChunks++;

                // 显示进度
                const progress = (resultReceiver.receivedChunks / resultReceiver.totalChunks * 100).toFixed(1);
                if (resultReceiver.receivedChunks % 10 === 0 || resultReceiver.receivedChunks === resultReceiver.totalChunks) {
                    addMessage(`📦 接收数据块 ${resultReceiver.receivedChunks}/${resultReceiver.totalChunks} (${progress}%)`, 'received');
                }
            }
        }

        // 处理AEC结果完成
        function handleAecResultComplete(data) {
            if (resultReceiver.receivedChunks === resultReceiver.totalChunks) {
                addMessage('✅ 所有数据块接收完成，正在组装音频...', 'received');

                try {
                    // 组装所有分块
                    const completeData = resultReceiver.chunks.join('');

                    // 解码base64数据
                    const binaryString = atob(completeData);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }

                    // 创建音频URL并设置到播放器
                    createOutputAudioUrl(bytes.buffer, resultReceiver.samples);

                    addMessage('🎵 回声消除后的音频已准备就绪，可以播放', 'received');

                } catch (error) {
                    addMessage('❌ 音频数据组装失败: ' + error.message, 'received');
                }
            } else {
                addMessage(`❌ 数据接收不完整: ${resultReceiver.receivedChunks}/${resultReceiver.totalChunks}`, 'received');
            }

            // 清理接收器
            resultReceiver.chunks = [];
            resultReceiver.totalChunks = 0;
            resultReceiver.receivedChunks = 0;
            resultReceiver.samples = 0;
        }

        // 创建输出音频URL
        function createOutputAudioUrl(arrayBuffer, samples) {
            // 创建WAV文件（假设48kHz单声道16bit PCM）
            const wavBuffer = createWavFile(arrayBuffer, 48000, 1, 16);
            const blob = new Blob([wavBuffer], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);

            const audioElement = document.getElementById('outputAudio');
            audioElement.src = url;

            // 启用下载按钮
            const downloadBtn = document.querySelector('.btn-secondary[onclick="downloadOutput()"]');
            if (downloadBtn) {
                downloadBtn.disabled = false;
                downloadBtn.textContent = '下载结果';
            }

            // 保存结果数据供下载使用
            window.outputAudioBlob = blob;
        }

        function handleAecStatusResponse(data) {
            isAecProcessing = data.is_processing;
            updateAecButtons();

            if (data.is_processing) {
                showProgress();
                updateProgress(
                    data.total_frames > 0 ? (data.progress * 100 / data.total_frames) : 0,
                    data.progress * 480 / 48000
                );
            } else {
                hideProgress();
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus('未连接', 'status-disconnected');
            updateMessageCount();

            // 初始化滑块显示
            updateSliderValue();

            // 初始化音频播放按钮状态
            updateAudioStreamButtons();

            // 从localStorage加载上次的WebSocket地址
            loadLastWebSocketUrl();

            // 初始化延时监控图表
            initDelayChart();

            // 初始化AEC按钮状态
            updateAecButtons();

            // 服务器地址回车键连接
            document.getElementById('serverUrl').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    connectWebSocket();
                }
            });
        });
    </script>
</body>
</html>
