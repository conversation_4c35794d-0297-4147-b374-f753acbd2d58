#include "websocket_server.h"
#include "aec_handler.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>
#include <unistd.h>
#include <time.h>
#include <pthread.h>

// 外部变量声明
extern volatile int g_aec_reference_delay_ms;          // AEC参考延时
extern int g_auto_adjust_enabled;             // 自动调整是否启用
extern int g_audio_streaming_enabled;         // 音频流是否启用
extern int g_audio_stream_clients;            // 音频流客户端数量

// WebSocket服务器全局变量
static struct lws_context *g_ws_context = NULL;
static struct lws *g_connected_clients[10];
static int g_client_count = 0;

// 音频流控制变量定义
int g_audio_streaming_enabled = 0;
int g_audio_stream_clients = 0;

// 消息缓冲区结构
typedef struct {
    char *buffer;
    size_t size;
    size_t capacity;
} message_buffer_t;

// 每个客户端的消息缓冲区
static message_buffer_t g_client_buffers[10] = {0};

/**
 * 获取客户端索引
 */
static int get_client_index(struct lws *wsi) {
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i] == wsi) {
            return i;
        }
    }
    return -1;
}

/**
 * 初始化客户端消息缓冲区
 */
static void init_client_buffer(int client_index) {
    if (client_index >= 0 && client_index < 10) {
        message_buffer_t *buf = &g_client_buffers[client_index];
        if (buf->buffer) {
            free(buf->buffer);
        }
        buf->capacity = MAX_WS_MESSAGE_SIZE * 2; // 2倍缓冲区
        buf->buffer = malloc(buf->capacity);
        buf->size = 0;
        if (buf->buffer) {
            buf->buffer[0] = '\0';
        }
    }
}

/**
 * 清理客户端消息缓冲区
 */
static void cleanup_client_buffer(int client_index) {
    if (client_index >= 0 && client_index < 10) {
        message_buffer_t *buf = &g_client_buffers[client_index];
        if (buf->buffer) {
            free(buf->buffer);
            buf->buffer = NULL;
        }
        buf->size = 0;
        buf->capacity = 0;
    }
}

/**
 * 添加数据到客户端缓冲区
 */
static int append_to_buffer(int client_index, const char *data, size_t len) {
    if (client_index < 0 || client_index >= 10) return -1;

    message_buffer_t *buf = &g_client_buffers[client_index];
    if (!buf->buffer) return -1;

    // 检查是否需要扩展缓冲区
    if (buf->size + len + 1 > buf->capacity) {
        size_t new_capacity = buf->capacity * 2;
        while (new_capacity < buf->size + len + 1) {
            new_capacity *= 2;
        }

        char *new_buffer = realloc(buf->buffer, new_capacity);
        if (!new_buffer) {
            LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to expand message buffer");
            return -1;
        }

        buf->buffer = new_buffer;
        buf->capacity = new_capacity;
    }

    // 添加数据
    memcpy(buf->buffer + buf->size, data, len);
    buf->size += len;
    buf->buffer[buf->size] = '\0';

    return 0;
}

/**
 * 检查缓冲区是否包含完整的JSON消息
 */
static char* extract_complete_json(int client_index) {
    if (client_index < 0 || client_index >= 10) return NULL;

    message_buffer_t *buf = &g_client_buffers[client_index];
    if (!buf->buffer || buf->size == 0) return NULL;

    // 简单检查：查找完整的JSON对象（以{开始，以}结束）
    char *start = strchr(buf->buffer, '{');
    if (!start) return NULL;

    int brace_count = 0;
    char *end = start;

    while (*end) {
        if (*end == '{') brace_count++;
        else if (*end == '}') brace_count--;

        if (brace_count == 0) {
            // 找到完整的JSON
            size_t json_len = end - start + 1;
            char *json = malloc(json_len + 1);
            if (json) {
                memcpy(json, start, json_len);
                json[json_len] = '\0';

                // 从缓冲区移除已处理的数据
                size_t remaining = buf->size - (end - buf->buffer + 1);
                if (remaining > 0) {
                    memmove(buf->buffer, end + 1, remaining);
                    buf->size = remaining;
                    buf->buffer[buf->size] = '\0';
                } else {
                    buf->size = 0;
                    buf->buffer[0] = '\0';
                }

                return json;
            }
            break;
        }
        end++;
    }

    return NULL;
}

/**
 * WebSocket协议回调函数 - 处理AEC控制
 */
int callback_aec_control(struct lws *wsi, enum lws_callback_reasons reason,
                        void *user __attribute__((unused)), void *in, size_t len) {
    static char response[4096]; // 使用较小的静态缓冲区用于响应
    unsigned char *buf = NULL;
    unsigned char *p = NULL;
    int response_len;

    switch (reason) {
        case LWS_CALLBACK_ESTABLISHED:
            // 新客户端连接
            if (g_client_count < 10) {
                g_connected_clients[g_client_count] = wsi;

                // 初始化客户端消息缓冲区
                init_client_buffer(g_client_count);

                g_client_count++;
                LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 WebSocket client connected (total: %d)", g_client_count);

                // 清理AEC上下文，为新连接准备
                aec_cleanup_context();

                // 发送当前AEC延时状态
                snprintf(response, sizeof(response),
                        "{\"type\":\"aec_delay_notify\",\"delay_ms\":%d,\"status\":\"connected\"}",
                        g_aec_reference_delay_ms);
                response_len = strlen(response);

                // 动态分配发送缓冲区
                buf = malloc(LWS_PRE + response_len);
                if (buf) {
                    p = &buf[LWS_PRE];
                    memcpy(p, response, response_len);
                    lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
                    free(buf);
                }
            }
            break;

        case LWS_CALLBACK_CLOSED:
            // 客户端断开连接
            for (int i = 0; i < g_client_count; i++) {
                if (g_connected_clients[i] == wsi) {
                    // 清理客户端消息缓冲区
                    cleanup_client_buffer(i);

                    // 移除断开的客户端
                    for (int j = i; j < g_client_count - 1; j++) {
                        g_connected_clients[j] = g_connected_clients[j + 1];
                        // 同时移动缓冲区
                        g_client_buffers[j] = g_client_buffers[j + 1];
                    }
                    g_client_count--;

                    // 清理最后一个位置
                    memset(&g_client_buffers[g_client_count], 0, sizeof(message_buffer_t));

                    // 清理AEC上下文
                    aec_cleanup_context();

                    // 如果这个客户端正在接收音频流，减少计数
                    if (g_audio_stream_clients > 0) {
                        g_audio_stream_clients--;
                        LOG_INFO(MODULE_AUDIO_REALTIME, "Audio stream client disconnected (remaining: %d)", g_audio_stream_clients);
                    }

                    LOG_INFO(MODULE_AUDIO_REALTIME, "🔌 WebSocket client disconnected (remaining: %d)", g_client_count);
                    break;
                }
            }
            break;

        case LWS_CALLBACK_RECEIVE:
            // 接收到客户端消息
            if (len > 0) {
                int client_index = get_client_index(wsi);
                if (client_index < 0) {
                    LOG_ERROR(MODULE_AUDIO_REALTIME, "Unknown client connection");
                    break;
                }

                // 将接收到的数据添加到缓冲区
                if (append_to_buffer(client_index, (const char*)in, len) < 0) {
                    LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to append data to buffer");
                    snprintf(response, sizeof(response), "{\"type\":\"error\",\"message\":\"Buffer overflow\"}");
                    goto send_response;
                }

                // 尝试提取完整的JSON消息
                char *complete_message = extract_complete_json(client_index);
                if (!complete_message) {
                    // 没有完整消息，等待更多数据
                    break;
                }

                LOG_DEBUG(MODULE_AUDIO_REALTIME, "Processing complete message: %.100s...", complete_message);

                // 解析JSON消息
                json_object *root = json_tokener_parse(complete_message);
                if (!root) {
                    LOG_ERROR(MODULE_AUDIO_REALTIME, "JSON parse failed for message: %.200s", complete_message);
                    snprintf(response, sizeof(response), "{\"type\":\"error\",\"message\":\"Invalid JSON format\"}");
                    free(complete_message);
                    goto send_response;
                } else {
                    json_object *type_obj;
                    if (json_object_object_get_ex(root, "type", &type_obj)) {
                        const char *msg_type = json_object_get_string(type_obj);
                        
                        // 处理不同类型的消息
                        if (strcmp(msg_type, "set_aec_delay") == 0) {
                            // 设置AEC延时
                            json_object *delay_obj;
                            if (json_object_object_get_ex(root, "delay_ms", &delay_obj)) {
                                int new_delay = json_object_get_int(delay_obj);
                                int old_delay = g_aec_reference_delay_ms;
                                g_aec_reference_delay_ms = new_delay;
                                
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_delay_response\",\"old_delay\":%d,\"new_delay\":%d,\"status\":\"success\"}",
                                        old_delay, new_delay);
                                
                                // 广播延时变化
                                websocket_broadcast_aec_delay_change(old_delay, new_delay);
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_delay_response\",\"status\":\"error\",\"message\":\"Missing delay_ms parameter\"}");
                            }
                            
                        } else if (strcmp(msg_type, "get_aec_delay") == 0) {
                            // 获取当前AEC延时
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"aec_delay_response\",\"delay_ms\":%d,\"status\":\"current\"}",
                                    g_aec_reference_delay_ms);
                            
                        } else if (strcmp(msg_type, "toggle_auto_adjust") == 0) {
                            // 切换自动调整
                            int old_enabled = g_auto_adjust_enabled;
                            g_auto_adjust_enabled = !g_auto_adjust_enabled;
                            
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"auto_adjust_response\",\"old_enabled\":%s,\"new_enabled\":%s,\"status\":\"toggled\"}",
                                    old_enabled ? "true" : "false", g_auto_adjust_enabled ? "true" : "false");
                            
                            // 广播自动调整状态变化
                            websocket_broadcast_auto_adjust_change(old_enabled, g_auto_adjust_enabled);
                            
                        } else if (strcmp(msg_type, "start_audio_stream") == 0) {
                            // 开始音频流
                            g_audio_streaming_enabled = 1;
                            g_audio_stream_clients++;
                            
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_stream_response\",\"status\":\"started\",\"clients\":%d}",
                                    g_audio_stream_clients);
                            
                        } else if (strcmp(msg_type, "stop_audio_stream") == 0) {
                            // 停止音频流
                            if (g_audio_stream_clients > 0) {
                                g_audio_stream_clients--;
                            }
                            if (g_audio_stream_clients == 0) {
                                g_audio_streaming_enabled = 0;
                            }
                            
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_stream_response\",\"status\":\"stopped\",\"clients\":%d}",
                                    g_audio_stream_clients);
                            
                        } else if (strcmp(msg_type, "start_mic_upload") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_mic_upload(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "upload_mic_chunk") == 0) {
                            // 委托给AEC处理器
                            aec_handle_upload_mic_chunk(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "start_ref_upload") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_ref_upload(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "upload_ref_chunk") == 0) {
                            // 委托给AEC处理器
                            aec_handle_upload_ref_chunk(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "start_aec_processing") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_processing(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "stop_aec_processing") == 0) {
                            // 委托给AEC处理器
                            aec_handle_stop_processing(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "get_aec_status") == 0) {
                            // 委托给AEC处理器
                            aec_handle_get_status(complete_message, response, sizeof(response));

                        } else if (strcmp(msg_type, "get_aec_result") == 0) {
                            // 委托给AEC处理器
                            aec_handle_get_result(complete_message, response, sizeof(response));
                            
                        } else if (strcmp(msg_type, "get_audio_status") == 0) {
                            // 获取音频状态
                            aec_processing_context_t *aec_ctx = aec_get_context();
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_status_response\",\"status\":\"running\",\"aec_delay_ms\":%d,\"auto_adjust_enabled\":%s,\"audio_streaming\":%s,\"aec_processing\":%s}",
                                    g_aec_reference_delay_ms, g_auto_adjust_enabled ? "true" : "false", 
                                    g_audio_streaming_enabled ? "true" : "false",
                                    aec_ctx->is_processing ? "true" : "false");
                            
                        } else {
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"error\",\"message\":\"Unknown message type: %s\"}", msg_type);
                        }
                    } else {
                        snprintf(response, sizeof(response),
                                "{\"type\":\"error\",\"message\":\"Missing type field\"}");
                    }
                    
                    json_object_put(root);
                }

                free(complete_message);

            send_response:
                // 发送响应
                if (strlen(response) > 0) {
                    response_len = strlen(response);

                    // 动态分配发送缓冲区
                    buf = malloc(LWS_PRE + response_len);
                    if (buf) {
                        p = &buf[LWS_PRE];
                        memcpy(p, response, response_len);
                        lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
                        free(buf);
                    } else {
                        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate send buffer");
                    }
                }
            }
            break;

        default:
            break;
    }

    return 0;
}

// WebSocket协议定义
static struct lws_protocols protocols[] = {
    {
        "aec-control-protocol",
        callback_aec_control,
        0,
        1024 * 1024, // 1MB接收缓冲区
        0, NULL, 0
    },
    { NULL, NULL, 0, 0, 0, NULL, 0 } // 必须以空终结
};

/**
 * 初始化WebSocket服务器
 */
int websocket_server_init(WebSocketServer *server, int port) {
    if (!server) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "WebSocket server pointer is NULL");
        return -1;
    }

    // 初始化服务器结构体
    memset(server, 0, sizeof(WebSocketServer));
    server->port = port;
    server->running = 0;

    // 初始化AEC处理器
    aec_handler_init();

    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    info.port = port;
    info.protocols = protocols;
    info.gid = -1;
    info.uid = -1;
    info.max_http_header_data = MAX_WS_MESSAGE_SIZE;
    info.max_http_header_pool = 16;
    info.options = LWS_SERVER_OPTION_VALIDATE_UTF8;

    g_ws_context = lws_create_context(&info);
    if (!g_ws_context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket context");
        return -1;
    }

    server->context = g_ws_context;
    g_client_count = 0;

    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server initialized on port %d", server->port);
    return 0;
}

/**
 * WebSocket服务器线程函数
 */
static void *websocket_server_thread(void *arg) {
    WebSocketServer *server = (WebSocketServer *)arg;

    LOG_INFO(MODULE_AUDIO_REALTIME, "🚀 WebSocket server thread started");

    while (server->running) {
        lws_service(server->context, 50); // 50ms超时
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "🛑 WebSocket server thread stopped");
    return NULL;
}

/**
 * 启动WebSocket服务器
 */
int websocket_server_start(WebSocketServer *server) {
    if (!server || !server->context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "WebSocket server not initialized");
        return -1;
    }

    server->running = 1;

    // 创建WebSocket服务线程
    if (pthread_create(&server->thread, NULL, websocket_server_thread, server) != 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket server thread");
        server->running = 0;
        return -1;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 WebSocket server started on port %d", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket protocol: aec-control-protocol");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 Connect: ws://localhost:%d", server->port);

    return 0;
}

/**
 * 运行WebSocket服务器事件循环
 */
void websocket_server_run(WebSocketServer *server) {
    if (!server || !server->context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "WebSocket server not initialized");
        return;
    }

    while (server->running) {
        lws_service(server->context, 50);
    }
}

/**
 * 停止WebSocket服务器
 */
void websocket_server_stop(WebSocketServer *server) {
    if (server && server->running) {
        server->running = 0;
        LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server stopping...");

        // 等待线程结束
        if (server->thread) {
            pthread_join(server->thread, NULL);
            server->thread = 0;
        }
    }
}

/**
 * 清理WebSocket服务器
 */
void websocket_server_cleanup(WebSocketServer *server) {
    if (server && server->context) {
        lws_context_destroy(server->context);
        server->context = NULL;
        g_ws_context = NULL;
        g_client_count = 0;

        // 清理AEC处理器
        aec_handler_cleanup();

        LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server cleaned up");
    }
}

/**
 * 广播AEC延时变化到所有客户端
 */
void websocket_broadcast_aec_delay_change(int old_delay, int new_delay) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_delay_notify\",\"old_delay\":%d,\"new_delay\":%d,\"timestamp\":%ld}",
             old_delay, new_delay, time(NULL));

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

/**
 * 广播自动调整状态变化到所有客户端
 */
void websocket_broadcast_auto_adjust_change(int old_enabled, int new_enabled) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"auto_adjust_notify\",\"old_enabled\":%s,\"new_enabled\":%s,\"timestamp\":%ld}",
             old_enabled ? "true" : "false", new_enabled ? "true" : "false", time(NULL));

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

/**
 * 广播AEC处理进度
 */
void websocket_broadcast_aec_progress(int progress, int total_frames) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    float percentage = total_frames > 0 ? (float)progress * 100.0f / total_frames : 0.0f;
    float seconds = (float)progress * AEC_FRAME_SIZE / AEC_SAMPLE_RATE;

    snprintf(message, sizeof(message),
             "{\"type\":\"aec_progress\",\"progress\":%d,\"total\":%d,\"percentage\":%.1f,\"seconds\":%.2f}",
             progress, total_frames, percentage, seconds);

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

/**
 * 广播AEC处理完成（包含数据）
 */
void websocket_broadcast_aec_complete_with_data(const char *output_data, int samples, int success) {
    if (!g_ws_context || g_client_count == 0) return;

    // 只发送完成通知，不包含实际数据
    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_complete\",\"success\":%s,\"samples\":%d,\"message\":\"%s\"}",
             success ? "true" : "false",
             samples,
             success ? "AEC处理完成，结果数据已保存" : "AEC处理失败");

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播完成通知到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);

    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC complete notification sent to %d clients", g_client_count);
}

/**
 * 广播通用消息
 */
void websocket_broadcast_message(const char *message) {
    if (!g_ws_context || g_client_count == 0 || !message) return;

    int message_len = strlen(message);
    unsigned char *buf = malloc(LWS_PRE + message_len);
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

// 其他广播函数的占位符实现
void websocket_broadcast_delay_estimate(float estimated_delay_ms, float mic_rms, float ref_rms,
                                       float correlation, int current_delay_ms) {
    // 实现延时估算广播
}

void websocket_broadcast_signal_strength(float mic_rms, float ref_rms, const char* status) {
    // 实现信号强度广播
}

void websocket_broadcast_audio_status(const char *status) {
    // 实现音频状态广播
}

/**
 * 发送音频数据到WebSocket客户端
 */
void websocket_send_audio_data(const uint8_t *pcm_data, size_t data_size,
                              int sample_rate, int channels) {
    if (!g_ws_context || g_client_count == 0 || !g_audio_streaming_enabled) {
        return;
    }

    // 简化实现：将音频数据编码为base64并发送
    char *encoded_data = malloc(data_size * 2); // base64编码后大约是原数据的1.33倍
    if (!encoded_data) {
        return;
    }

    // 简化：直接使用十六进制编码而不是base64
    int encoded_len = 0;
    for (size_t i = 0; i < data_size && encoded_len < (int)(data_size * 2) - 2; i++) {
        encoded_len += snprintf(encoded_data + encoded_len, 3, "%02x", pcm_data[i]);
    }
    if (encoded_len > 0) {
        char message[MAX_WS_MESSAGE_SIZE];
        int samples = data_size / (sizeof(int16_t) * channels);
        snprintf(message, sizeof(message),
                "{\"type\":\"audio_data\",\"samples\":%d,\"sample_rate\":%d,\"channels\":%d,\"data\":\"%s\"}",
                samples, sample_rate, channels, encoded_data);

        unsigned char *buf = malloc(LWS_PRE + strlen(message));
        if (buf) {
            unsigned char *p = &buf[LWS_PRE];
            int message_len = strlen(message);
            memcpy(p, message, message_len);

            // 发送到所有音频流客户端
            for (int i = 0; i < g_client_count; i++) {
                if (g_connected_clients[i]) {
                    lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
                }
            }
            free(buf);
        }
    }

    free(encoded_data);
}


