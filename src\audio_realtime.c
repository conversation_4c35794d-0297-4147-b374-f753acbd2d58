#include "audio_realtime.h"
#include "audio_processor.h"
#include "voice_dialog.h"
#include "app_resampler.h"
#include "voice_events.h"
#include "logger.h"
#include "http_server.h"
#include "websocket_server.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <errno.h>
#include <math.h>

#ifndef _WIN32

#include <unistd.h>
#include <sched.h>
#include <sys/mman.h>
#include <alsa/asoundlib.h>

#endif

// 全局语音对话处理器
static VoiceDialogProcessor *g_voice_dialog = NULL;

// 全局应用重采样器（48kHz→16kHz）
static AppResamplerWrapper g_resampler;

// 全局HTTP服务器
static HttpServer g_http_server;

// 全局WebSocket服务器
static WebSocketServer g_websocket_server;

// AEC延时相关的全局缓存变量
static int g_cached_required_ref_samples = 0;  // 缓存的所需参考样本数
static int g_last_cached_delay_ms = -1;        // 上次缓存时的延时值

// 延时自适应调整相关变量
static float g_delay_estimates[10] = {0};      // 最近10次延时估计结果
static int g_delay_estimate_index = 0;         // 当前估计结果索引
static int g_delay_estimate_count = 0;         // 有效估计结果数量
int g_auto_adjust_enabled = 1;                 // 是否启用自动调整（导出给HTTP API使用）
volatile int g_aec_reference_delay_ms = 9;     // AEC参考延时（毫秒）

// 获取全局重采样器的接口函数
AppResamplerWrapper *get_global_resampler(void) {
    return &g_resampler;
}

/**
 * 创建循环缓冲区
 */
RingBuffer *ring_buffer_create(int size) {
    RingBuffer *rb = malloc(sizeof(RingBuffer));
    if (!rb) return NULL;

    rb->buffer = malloc(size * sizeof(int16_t));
    if (!rb->buffer) {
        free(rb);
        return NULL;
    }

    rb->size = size;
    rb->write_pos = 0;
    rb->read_pos = 0;
    rb->available = 0;

    pthread_mutex_init(&rb->mutex, NULL);
    pthread_cond_init(&rb->cond_not_empty, NULL);
    pthread_cond_init(&rb->cond_not_full, NULL);

    return rb;
}

/**
 * 销毁循环缓冲区
 */
void ring_buffer_destroy(RingBuffer *rb) {
    if (!rb) return;

    pthread_mutex_destroy(&rb->mutex);
    pthread_cond_destroy(&rb->cond_not_empty);
    pthread_cond_destroy(&rb->cond_not_full);

    free(rb->buffer);
    free(rb);
}

/**
 * 向循环缓冲区写入数据
 */
int ring_buffer_write(RingBuffer *rb, const int16_t *data, int samples) {
    if (!rb || !data) return -1;

    pthread_mutex_lock(&rb->mutex);

    // 等待有足够空间
    while (rb->available + samples > rb->size) {
        pthread_cond_wait(&rb->cond_not_full, &rb->mutex);
    }

    // 写入数据
    for (int i = 0; i < samples; i++) {
        rb->buffer[rb->write_pos] = data[i];
        rb->write_pos = (rb->write_pos + 1) % rb->size;
    }

    rb->available += samples;

    pthread_cond_signal(&rb->cond_not_empty);
    pthread_mutex_unlock(&rb->mutex);

    return samples;
}

/**
 * 从循环缓冲区读取数据
 */
int ring_buffer_read(RingBuffer *rb, int16_t *data, int samples) {
    if (!rb || !data) return -1;

    pthread_mutex_lock(&rb->mutex);

    // 等待有足够数据
    while (rb->available < samples) {
        pthread_cond_wait(&rb->cond_not_empty, &rb->mutex);
    }

    // 读取数据
    for (int i = 0; i < samples; i++) {
        data[i] = rb->buffer[rb->read_pos];
        rb->read_pos = (rb->read_pos + 1) % rb->size;
    }

    rb->available -= samples;

    pthread_cond_signal(&rb->cond_not_full);
    pthread_mutex_unlock(&rb->mutex);

    return samples;
}

/**
 * 获取可读数据量
 */
int ring_buffer_available_read(RingBuffer *rb) {
    if (!rb) return 0;

    pthread_mutex_lock(&rb->mutex);
    int available = rb->available;
    pthread_mutex_unlock(&rb->mutex);

    return available;
}

/**
 * 高效地推进（丢弃）环形缓冲区中的数据
 */
int ring_buffer_advance_read_pos(RingBuffer *rb, int samples) {
    if (!rb) return -1;

    pthread_mutex_lock(&rb->mutex);

    if (samples > rb->available) {
        samples = rb->available;
    }

    rb->read_pos = (rb->read_pos + samples) % rb->size;
    rb->available -= samples;

    pthread_cond_signal(&rb->cond_not_full);
    pthread_mutex_unlock(&rb->mutex);

    return samples;
}

/**
 * 打印ALSA错误信息
 */
void print_alsa_error(const char *function, int err) {
    fprintf(stderr, "ALSA error in %s: %s", function, snd_strerror(err));
}

/**
 * 打印设备支持的格式信息
 */
void print_device_capabilities(snd_pcm_t *handle, const char *device_name) {
    snd_pcm_hw_params_t *hw_params;
    int err;

    LOG_INFO(MODULE_AUDIO_REALTIME, "=== Device Capabilities: %s ===", device_name);

    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        print_alsa_error("snd_pcm_hw_params_malloc", err);
        return;
    }

    if ((err = snd_pcm_hw_params_any(handle, hw_params)) < 0) {
        print_alsa_error("snd_pcm_hw_params_any", err);
        snd_pcm_hw_params_free(hw_params);
        return;
    }

    // 检查支持的格式
    LOG_INFO(MODULE_AUDIO_REALTIME, "Supported formats: ");
    snd_pcm_format_t formats[] = {
            SND_PCM_FORMAT_S8, SND_PCM_FORMAT_U8,
            SND_PCM_FORMAT_S16_LE, SND_PCM_FORMAT_S16_BE,
            SND_PCM_FORMAT_U16_LE, SND_PCM_FORMAT_U16_BE,
            SND_PCM_FORMAT_S24_LE, SND_PCM_FORMAT_S24_BE,
            SND_PCM_FORMAT_S32_LE, SND_PCM_FORMAT_S32_BE,
            SND_PCM_FORMAT_FLOAT_LE, SND_PCM_FORMAT_FLOAT_BE
    };

    for (int i = 0; i < 12; i++) {
        if (snd_pcm_hw_params_test_format(handle, hw_params, formats[i]) == 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "%s ", snd_pcm_format_name(formats[i]));
        }
    }
    LOG_INFO(MODULE_AUDIO_REALTIME, "");

    // 检查支持的声道数
    unsigned int min_channels, max_channels;
    if (snd_pcm_hw_params_get_channels_min(hw_params, &min_channels) == 0 &&
        snd_pcm_hw_params_get_channels_max(hw_params, &max_channels) == 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Supported channels: %u - %u", min_channels, max_channels);
    }

    // 检查支持的采样率
    unsigned int min_rate, max_rate;
    if (snd_pcm_hw_params_get_rate_min(hw_params, &min_rate, 0) == 0 &&
        snd_pcm_hw_params_get_rate_max(hw_params, &max_rate, 0) == 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Supported sample rates: %u - %u Hz", min_rate, max_rate);
    }

    snd_pcm_hw_params_free(hw_params);
    LOG_INFO(MODULE_AUDIO_REALTIME, "===============================");
}

/**
 * 配置ALSA录音设备 - 使用简化的snd_pcm_set_params
 */
int configure_alsa_capture(snd_pcm_t *handle) {
    int err;

    // 使用snd_pcm_set_params简化配置
    // 参数：handle, format, access, channels, rate, soft_resample, latency_us
    unsigned int rate = 48000;      // 使用常见的采样率
    unsigned int channels = 2;      // 2声道麦克风（实际硬件）
    unsigned int latency = 100000;  // 100ms延迟，减少underrun

    if ((err = snd_pcm_set_params(handle,
                                  SND_PCM_FORMAT_S16_LE,  // 16位格式
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  channels,
                                  rate,
                                  1,  // 允许重采样
                                  latency)) < 0) {
        print_alsa_error("snd_pcm_set_params capture", err);
        return -1;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Capture device configured: S16_LE, %uch, %uHz, latency=%uus",
             channels, rate, latency);

    // 获取实际配置的参数
    snd_pcm_hw_params_t *hw_params;
    snd_pcm_hw_params_alloca(&hw_params);
    if (snd_pcm_hw_params_current(handle, hw_params) >= 0) {
        unsigned int actual_rate;
        unsigned int actual_channels;
        snd_pcm_hw_params_get_rate(hw_params, &actual_rate, 0);
        snd_pcm_hw_params_get_channels(hw_params, &actual_channels);
        LOG_INFO(MODULE_AUDIO_REALTIME, "Actual device format: %uch, %uHz", actual_channels, actual_rate);
    }

    return 0;
}

/**
 * 配置ALSA播放设备
 */
int configure_alsa_playback(snd_pcm_t *handle) {
    int err;

    // 使用snd_pcm_set_params简化配置
    unsigned int rate = PLAY_SAMPLE_RATE;      // 使用常见的采样率
    unsigned int channels = PLAY_CHANNELS;      // 立体声
    unsigned int latency = 100000;  // 100ms延迟

    if ((err = snd_pcm_set_params(handle,
                                  SND_PCM_FORMAT_S16_LE,  // 16位格式
                                  SND_PCM_ACCESS_RW_INTERLEAVED,
                                  channels,
                                  rate,
                                  1,  // 允许重采样
                                  latency)) < 0) {
        print_alsa_error("snd_pcm_set_params playback", err);
        return -1;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Playback device configured: S16_LE, %uch, %uHz, latency=%uus",
             channels, rate, latency);

    return 0;
}

/**
 * 查找USB麦克风设备
 */
int find_usb_mic_device(char *device_name, size_t name_size) {
    snd_ctl_t *handle;
    snd_ctl_card_info_t *info;
    snd_pcm_info_t *pcminfo;
    int card = -1;
    int err;

    snd_ctl_card_info_alloca(&info);
    snd_pcm_info_alloca(&pcminfo);

    // 遍历所有声卡
    while (snd_card_next(&card) >= 0 && card >= 0) {
        char name[32];
        snprintf(name, sizeof(name), "hw:%d", card);

        if ((err = snd_ctl_open(&handle, name, 0)) < 0) {
            continue;
        }

        if ((err = snd_ctl_card_info(handle, info)) < 0) {
            snd_ctl_close(handle);
            continue;
        }

        const char *card_name = snd_ctl_card_info_get_name(info);
        LOG_INFO(MODULE_AUDIO_REALTIME, "Found card %d: %s", card, card_name);

        // 检查是否是USB设备或包含"USB"字样
        if (strstr(card_name, "USB") || strstr(card_name, "usb") ||
            strstr(card_name, "Audio") || card == 2) {  // 通常USB音频设备是card 2
            // 检查是否有录音设备
            int dev = -1;
            while (snd_ctl_pcm_next_device(handle, &dev) >= 0 && dev >= 0) {
                snd_pcm_info_set_device(pcminfo, dev);
                snd_pcm_info_set_subdevice(pcminfo, 0);
                snd_pcm_info_set_stream(pcminfo, SND_PCM_STREAM_CAPTURE);

                if (snd_ctl_pcm_info(handle, pcminfo) >= 0) {
                    snprintf(device_name, name_size, "plughw:%d,%d", card, dev);
                    LOG_INFO(MODULE_AUDIO_REALTIME, "Found USB capture device: %s (%s)", device_name, card_name);
                    snd_ctl_close(handle);
                    return 0;
                }
            }

            // 如果没有找到特定设备，尝试使用card本身
            snprintf(device_name, name_size, "plughw:%d", card);
            LOG_INFO(MODULE_AUDIO_REALTIME, "Using USB card device: %s (%s)", device_name, card_name);
            snd_ctl_close(handle);
            return 0;
        }

        snd_ctl_close(handle);
    }

    // 如果没找到USB设备，使用默认设备
    snprintf(device_name, name_size, "default");
    LOG_INFO(MODULE_AUDIO_REALTIME, "No USB device found, using default capture device");
    return 0;
}

/**
 * 列出所有音频设备
 */
int list_audio_devices(void) {
    snd_ctl_t *handle;
    snd_ctl_card_info_t *info;
    snd_pcm_info_t *pcminfo;
    int card = -1;
    int err;

    snd_ctl_card_info_alloca(&info);
    snd_pcm_info_alloca(&pcminfo);

    LOG_INFO(MODULE_AUDIO_REALTIME, "=== Available Audio Devices ===");

    while (snd_card_next(&card) >= 0 && card >= 0) {
        char name[32];
        snprintf(name, sizeof(name), "hw:%d", card);

        if ((err = snd_ctl_open(&handle, name, 0)) < 0) {
            continue;
        }

        if ((err = snd_ctl_card_info(handle, info)) < 0) {
            snd_ctl_close(handle);
            continue;
        }

        LOG_INFO(MODULE_AUDIO_REALTIME, "Card %d: %s", card, snd_ctl_card_info_get_name(info));

        // 列出录音设备
        int dev = -1;
        while (snd_ctl_pcm_next_device(handle, &dev) >= 0 && dev >= 0) {
            snd_pcm_info_set_device(pcminfo, dev);
            snd_pcm_info_set_subdevice(pcminfo, 0);
            snd_pcm_info_set_stream(pcminfo, SND_PCM_STREAM_CAPTURE);

            if (snd_ctl_pcm_info(handle, pcminfo) >= 0) {
                LOG_INFO(MODULE_AUDIO_REALTIME, "  Capture hw:%d,%d: %s", card, dev,
                         snd_pcm_info_get_name(pcminfo));
            }

            snd_pcm_info_set_stream(pcminfo, SND_PCM_STREAM_PLAYBACK);
            if (snd_ctl_pcm_info(handle, pcminfo) >= 0) {
                LOG_INFO(MODULE_AUDIO_REALTIME, "  Playback hw:%d,%d: %s", card, dev,
                         snd_pcm_info_get_name(pcminfo));
            }
        }

        snd_ctl_close(handle);
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "==============================");
    return 0;
}

/**
 * 同步采集线程函数 - 同时采集麦克风和扬声器输出
 */
void *synchronized_capture_thread_func(void *arg) {
    RealtimeAudioProcessor *rt = (RealtimeAudioProcessor *) arg;
    int16_t mic_buffer[ALSA_FRAME_SIZE * ALSA_CHANNELS];
    int16_t spk_buffer[PLAY_FRAME_SIZE * PLAY_CHANNELS];
    int mic_err, spk_err;

    // 扬声器数据累积缓冲区（静态变量，在函数调用间保持状态）
    static int spk_accumulated_samples = 0;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Synchronized capture thread started (mic + speaker monitor)");

    while (rt->running) {
        // 几乎同时读取麦克风和扬声器监听数据
        mic_err = snd_pcm_readi(rt->capture_handle, mic_buffer, ALSA_FRAME_SIZE);

#if ENABLE_AEC_PLAYBACK_CAPTURE
        spk_err = snd_pcm_readi(rt->aec_capture_handle, spk_buffer, ALSA_FRAME_SIZE);
#endif

        // 处理麦克风数据
        if (mic_err == -EPIPE) {
            static int mic_underrun_count = 0;
            mic_underrun_count++;
            if (mic_underrun_count % 100 == 1) {
                LOG_INFO(MODULE_AUDIO_REALTIME, "Mic underrun occurred (count: %d)", mic_underrun_count);
            }
            if ((mic_err = snd_pcm_prepare(rt->capture_handle)) < 0) {
                print_alsa_error("snd_pcm_prepare mic after underrun", mic_err);
                break;
            }
            rt->underruns++;
            continue;
        } else if (mic_err == -EAGAIN) {
            usleep(1000);
            continue;
        } else if (mic_err < 0) {
            print_alsa_error("snd_pcm_readi mic", mic_err);
            if ((mic_err = snd_pcm_recover(rt->capture_handle, mic_err, 0)) < 0) {
                print_alsa_error("snd_pcm_recover mic", mic_err);
                break;
            }
            continue;
        } else if (mic_err > 0) {
            // 写入麦克风数据到输入环形缓冲区
            if (ring_buffer_write(rt->input_ring, mic_buffer, mic_err * ALSA_CHANNELS) < 0) {
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to write to input ring buffer");
                rt->overruns++;
            } else {
                rt->frames_captured++;
            }
        }

#if ENABLE_AEC_PLAYBACK_CAPTURE
        // 处理扬声器监听数据
        if (spk_err == -EPIPE) {
            static int spk_underrun_count = 0;
            spk_underrun_count++;
            if (spk_underrun_count % 100 == 1) {
                LOG_INFO(MODULE_AUDIO_REALTIME, "Speaker monitor underrun (count: %d)", spk_underrun_count);
            }
            if ((spk_err = snd_pcm_prepare(rt->aec_capture_handle)) < 0) {
                print_alsa_error("snd_pcm_prepare speaker after underrun", spk_err);
                // 不要break，扬声器监听失败不应该停止整个采集
            }
        } else if (spk_err == -EAGAIN) {
            // 扬声器监听暂时无数据，继续处理麦克风
        } else if (spk_err < 0) {
            print_alsa_error("snd_pcm_readi speaker", spk_err);
            if (snd_pcm_recover(rt->aec_capture_handle, spk_err, 0) < 0) {
                LOG_INFO(MODULE_AUDIO_REALTIME, "Speaker monitor recovery failed, continuing");
            }
        } else if (spk_err > 0) {
            if (ring_buffer_write(rt->playback_capture_ring, spk_buffer,
                                  mic_err * ALSA_CHANNELS) < 0) {
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to write to AEC reference ring buffer");
                break;
            }
        }
#endif
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Synchronized capture thread stopped");
    return NULL;
}



/**
 * 播放线程函数
 */
void *playback_thread_func(void *arg) {
    RealtimeAudioProcessor *rt = (RealtimeAudioProcessor *) arg;
    int16_t buffer[PLAY_FRAME_SIZE * PLAY_CHANNELS];
    int err;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Playback thread started");

    while (rt->running) {
        // 从循环缓冲区读取数据
        if (ring_buffer_read(rt->output_ring, buffer, PLAY_FRAME_SIZE * PLAY_CHANNELS) < 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "Failed to read from output ring buffer");
            continue;
        }

        // 写入ALSA播放设备
        err = snd_pcm_writei(rt->playback_handle, buffer, PLAY_FRAME_SIZE);

        if (err == -EPIPE) {
            // 缓冲区溢出 - 减少日志输出频率
            static int playback_underrun_count = 0;
            playback_underrun_count++;
            if (playback_underrun_count % 100 == 1) {  // 每100次只打印一次
                LOG_INFO(MODULE_AUDIO_REALTIME, "Playback underrun occurred (count: %d)", playback_underrun_count);
            }

            // 尝试恢复
            if ((err = snd_pcm_prepare(rt->playback_handle)) < 0) {
                print_alsa_error("snd_pcm_prepare playback after underrun", err);
                break;
            }

            rt->underruns++;
            continue;
        } else if (err == -EAGAIN) {
            // 暂时无法写入
            usleep(1000);  // 等待1ms
            continue;
        } else if (err < 0) {
            print_alsa_error("snd_pcm_writei", err);

            // 尝试恢复
            if ((err = snd_pcm_recover(rt->playback_handle, err, 0)) < 0) {
                print_alsa_error("snd_pcm_recover playback", err);
                break;
            }
            continue;
        } else if (err != PLAY_FRAME_SIZE) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "Warning: Wrote %d frames, expected %d", err, PLAY_FRAME_SIZE);
        }

        rt->frames_played++;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Playback thread stopped");
    return NULL;
}



#define MIN_RMS_THRESHOLD 10.0f // 最小 RMS 阈值（降低以适应实际信号强度）

// 计算信号 RMS
float calc_rms(int16_t *buffer, int length) {
    double sum = 0.0;
    for (int i = 0; i < length; i++) {
        sum += buffer[i] * buffer[i];
    }
    return sqrt(sum / length);
}

// 改进的延迟估计函数
int estimate_delay(int16_t *mic_buffer, int16_t *ref_buffer, int length, int sample_rate, int channels) {
    // 混合立体声为单声道
    int16_t *mic_mono = (int16_t *)malloc(length * sizeof(int16_t));
    int16_t *ref_mono = (int16_t *)malloc(length * sizeof(int16_t));
    if (!mic_mono || !ref_mono) {
        free(mic_mono);
        free(ref_mono);
        LOG_ERROR(MODULE_AUDIO_REALTIME, "内存分配失败");
        return -1;
    }

    for (int i = 0; i < length; i++) {
        if (channels == 2) {
            mic_mono[i] = (mic_buffer[i * 2] + mic_buffer[i * 2 + 1]) / 2; // 混合麦克风通道
            ref_mono[i] = (ref_buffer[i * 2] + ref_buffer[i * 2 + 1]) / 2; // 混合参考通道
        } else {
            mic_mono[i] = mic_buffer[i];
            ref_mono[i] = ref_buffer[i];
        }
    }

    // 检查信号能量
    float mic_rms = calc_rms(mic_mono, length);
    float ref_rms = calc_rms(ref_mono, length);

    // 定期广播信号强度信息（每100次调用广播一次）
    static int signal_broadcast_counter = 0;
    signal_broadcast_counter++;
    if (signal_broadcast_counter >= 100) {
        signal_broadcast_counter = 0;
        const char* status = (mic_rms < MIN_RMS_THRESHOLD || ref_rms < MIN_RMS_THRESHOLD) ? "weak" : "good";
        websocket_broadcast_signal_strength(mic_rms, ref_rms, status);
    }

    if (mic_rms < MIN_RMS_THRESHOLD || ref_rms < MIN_RMS_THRESHOLD) {
        // 降低日志级别，避免频繁输出
        LOG_DEBUG(MODULE_AUDIO_REALTIME, "信号太弱: mic_rms=%.2f, ref_rms=%.2f", mic_rms, ref_rms);
        free(mic_mono);
        free(ref_mono);
        return -1; // 信号无效
    }

    // 归一化信号
    float *mic_norm = (float *)malloc(length * sizeof(float));
    float *ref_norm = (float *)malloc(length * sizeof(float));
    if (!mic_norm || !ref_norm) {
        free(mic_mono);
        free(ref_mono);
        free(mic_norm);
        free(ref_norm);
        LOG_ERROR(MODULE_AUDIO_REALTIME, "内存分配失败");
        return -1;
    }

    for (int i = 0; i < length; i++) {
        mic_norm[i] = mic_mono[i] / (mic_rms + 1e-6f);
        ref_norm[i] = ref_mono[i] / (ref_rms + 1e-6f);
    }

    // 互相关计算（降采样）
    int max_delay = sample_rate / 1000 * 200; // 最大 200ms
    int step = 4; // 降采样因子
    int best_delay = -1;
    float max_corr = -1.0f;

    // 自适应相关性阈值：信号越弱，阈值越低
    float corr_threshold = 0.3f; // 默认阈值
    if (mic_rms < 50.0f || ref_rms < 50.0f) {
        corr_threshold = 0.15f; // 弱信号时降低阈值
    }
    if (mic_rms < 20.0f || ref_rms < 20.0f) {
        corr_threshold = 0.1f; // 很弱信号时进一步降低阈值
    }

    for (int d = 0; d < max_delay; d += step) {
        float corr = 0.0f;
        int count = 0;
        for (int i = 0; i < length - d; i += step) {
            corr += mic_norm[i + d] * ref_norm[i];
            count++;
        }
        corr /= (count > 0) ? count : 1;
        if (corr > max_corr && corr > corr_threshold) {
            max_corr = corr;
            best_delay = d;
        }
    }

    free(mic_mono);
    free(ref_mono);
    free(mic_norm);
    free(ref_norm);

    if (best_delay == -1) {
        LOG_DEBUG(MODULE_AUDIO_REALTIME, "无有效相关性峰值 (max_corr=%.3f, threshold=%.3f)", max_corr, corr_threshold);
        return -1;
    }

    // 广播延时估计结果到WebSocket客户端（每10次成功估计广播一次）
    static int estimate_broadcast_counter = 0;
    estimate_broadcast_counter++;
    if (estimate_broadcast_counter >= 10) {
        estimate_broadcast_counter = 0;
        float estimated_delay_ms = best_delay * 1000.0f / sample_rate;
        websocket_broadcast_delay_estimate(estimated_delay_ms, mic_rms, ref_rms, max_corr, g_aec_reference_delay_ms);

        LOG_DEBUG(MODULE_AUDIO_REALTIME, "延时估计: %d samples (%.1fms), corr=%.3f, mic_rms=%.1f, ref_rms=%.1f",
                 best_delay, estimated_delay_ms, max_corr, mic_rms, ref_rms);
    }

    return best_delay;
}

/**
 * 添加延时估计结果并检查是否需要自动调整
 */
void add_delay_estimate(float estimated_delay_ms) {
    if (!g_auto_adjust_enabled) {
        // 记录自动调整被禁用的情况
        static int disabled_log_counter = 0;
        disabled_log_counter++;
        if (disabled_log_counter % 50 == 1) { // 每50次记录一次
            LOG_INFO(MODULE_AUDIO_REALTIME, "🚫 Auto-adjust disabled, estimated: %.1f ms", estimated_delay_ms);
        }
        return;
    }

    LOG_DEBUG(MODULE_AUDIO_REALTIME, "📊 Adding delay estimate: %.1f ms (enabled=%d)",
             estimated_delay_ms, g_auto_adjust_enabled);

    // 添加新的估计结果
    g_delay_estimates[g_delay_estimate_index] = estimated_delay_ms;
    g_delay_estimate_index = (g_delay_estimate_index + 1) % 10;
    if (g_delay_estimate_count < 10) {
        g_delay_estimate_count++;
    }

    // 需要至少5个有效估计结果才进行调整
    if (g_delay_estimate_count < 5) {
        LOG_DEBUG(MODULE_AUDIO_REALTIME, "🔄 Auto-adjust: collecting data (%d/5), latest: %.1f ms",
                 g_delay_estimate_count, estimated_delay_ms);
        return;
    }

    // 计算平均延时
    float sum = 0.0f;
    int valid_count = 0;
    for (int i = 0; i < g_delay_estimate_count; i++) {
        if (g_delay_estimates[i] > 0) {
            sum += g_delay_estimates[i];
            valid_count++;
        }
    }

    if (valid_count < 3) {
        LOG_DEBUG(MODULE_AUDIO_REALTIME, "🔄 Auto-adjust: insufficient valid data (%d/3)", valid_count);
        return; // 需要至少3个有效值
    }

    float avg_delay_ms = sum / valid_count;
    float current_delay_ms = (float)g_aec_reference_delay_ms;
    float diff = fabs(avg_delay_ms - current_delay_ms);

    LOG_INFO(MODULE_AUDIO_REALTIME, "🔍 Auto-adjust analysis: avg=%.1fms, current=%dms, diff=%.1fms, samples=%d",
             avg_delay_ms, g_aec_reference_delay_ms, diff, valid_count);

    // 如果平均延时与当前设置差异超过15ms，且估计值在合理范围内，则自动调整
    // 放宽范围：允许1-200ms的延时调整
    if (diff > 15.0f && avg_delay_ms >= 1.0f && avg_delay_ms <= 200.0f) {
        // 对于很小的延时值，添加一些安全余量
        int new_delay_ms;
        if (avg_delay_ms < 5.0f) {
            new_delay_ms = (int)(avg_delay_ms + 5.0f); // 很小延时加5ms余量
        } else if (avg_delay_ms < 15.0f) {
            new_delay_ms = (int)(avg_delay_ms + 3.0f); // 小延时加3ms余量
        } else {
            new_delay_ms = (int)(avg_delay_ms + 0.5f); // 四舍五入
        }

        // 确保最小值为1ms
        if (new_delay_ms < 1) {
            new_delay_ms = 1;
        }

        LOG_INFO(MODULE_AUDIO_REALTIME,
               "🔧 Auto-adjusting AEC delay: %d ms → %d ms (avg estimated: %.1f ms, samples: %d)",
               g_aec_reference_delay_ms, new_delay_ms, avg_delay_ms, valid_count);

        // 更新延时设置
        g_aec_reference_delay_ms = new_delay_ms;

        // 通过WebSocket广播延时变化
        websocket_broadcast_aec_delay_change(current_delay_ms, new_delay_ms);

        // 重置估计历史，避免频繁调整
        g_delay_estimate_count = 0;
        g_delay_estimate_index = 0;
    } else if (diff > 15.0f) {
        // 记录为什么没有调整
        LOG_INFO(MODULE_AUDIO_REALTIME,
               "⚠️ AEC delay not adjusted: avg estimated %.1f ms (range: 5-200ms), diff: %.1f ms",
               avg_delay_ms, diff);
    }
}



/**
 * 音频处理线程函数
 */
void *process_thread_func(void *arg) {
    RealtimeAudioProcessor *rt = (RealtimeAudioProcessor *) arg;
    AudioProcessor *processor = (AudioProcessor *) rt->audio_processor;

    // ALSA输入缓冲区（48kHz立体声）
    int16_t alsa_input_buffer[ALSA_FRAME_SIZE * ALSA_CHANNELS];

    // 回声消除后的立体声缓冲区（48kHz）
    int16_t aec_stereo_buffer[ALSA_FRAME_SIZE * ALSA_CHANNELS];

    // 波束形成后的单声道输出缓冲区（48kHz）
    int16_t beamformed_output_buffer[ALSA_FRAME_SIZE];

    int16_t speex_processed_buffer[ALSA_FRAME_SIZE];
    int16_t resampled_buffer[RT_FRAME_SIZE];
    int16_t speex_output[RT_FRAME_SIZE];
    int16_t reference_buffer[ALSA_FRAME_SIZE]; // 扬声器参考信号 (48kHz, 单声道)

#if ENABLE_AEC_PLAYBACK_CAPTURE
    // AEC参考信号处理缓冲区 (48kHz, 2声道)
    int16_t ref_stereo_48k[ALSA_FRAME_SIZE * PLAY_CHANNELS];
    // 动态计算所需的参考样本数（不再是const，每次循环都重新计算）
    int required_ref_samples;
#endif

    // VAD预热帧数，防止启动时误触发
    int vad_warmup_frames = 0;
    const int VAD_WARMUP_COUNT = 50; // 约0.5秒

    // 清除
    memset(reference_buffer, 0x00, ALSA_FRAME_SIZE );

    // 输出缓冲区（用于扬声器播放）
    int16_t output_buffer[ALSA_FRAME_SIZE * ALSA_CHANNELS];

    const int cbuf_len = ALSA_FRAME_SIZE * ALSA_CHANNELS ;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Process thread started");

    while (rt->running) {
        // 从输入缓冲区读取数据（48kHz立体声）
        if (ring_buffer_read(rt->input_ring, alsa_input_buffer, ALSA_FRAME_SIZE * ALSA_CHANNELS) < 0) {
            continue;
        }

#if ENABLE_AEC_PLAYBACK_CAPTURE
        // 检查延时参数是否变化，只有变化时才重新计算
        if (g_last_cached_delay_ms != g_aec_reference_delay_ms) {
            // 重新计算所需的参考样本数（修正：延时不应该乘以声道数）
            g_cached_required_ref_samples =  g_aec_reference_delay_ms * (PLAY_SAMPLE_RATE / 1000);

            LOG_INFO(MODULE_AUDIO_REALTIME, "🔄 AEC delay changed: %d ms → %d ms (required samples: %d)",
                     g_last_cached_delay_ms, g_aec_reference_delay_ms, g_cached_required_ref_samples);

            // 通过WebSocket广播延时变化
            websocket_broadcast_aec_delay_change(g_last_cached_delay_ms, g_aec_reference_delay_ms);

            g_last_cached_delay_ms = g_aec_reference_delay_ms;
        }

        // 使用缓存的计算结果
        required_ref_samples = g_cached_required_ref_samples;
        int frame_samples = ALSA_FRAME_SIZE * PLAY_CHANNELS;
        int total_needed = required_ref_samples + frame_samples;

        // 准备AEC参考信号，并处理延时
        int available_samples = ring_buffer_available_read(rt->playback_capture_ring);
        if (available_samples >= total_needed) {
            // 计算实际需要跳过的立体声样本数
            int stereo_samples_to_skip = required_ref_samples * PLAY_CHANNELS;

            // 1) 丢弃延时对应的样本（立体声格式）
            ring_buffer_advance_read_pos(rt->playback_capture_ring, stereo_samples_to_skip);

            // 2) 再读取真正对齐好的一帧
            int read_ref_len = ring_buffer_read(rt->playback_capture_ring,
                                                ref_stereo_48k,
                                                frame_samples);
            if (read_ref_len == frame_samples) {
                // 混合左右声道，送给 AEC
                for (int i = 0; i < frame_samples/2; i++) {
                    reference_buffer[i] =
                            (ref_stereo_48k[2*i] + ref_stereo_48k[2*i+1]) / 2;
                }

                // 执行回声消除
                audio_echo_cancel(processor,
                                  alsa_input_buffer,
                                  reference_buffer,
                                  aec_stereo_buffer);

                // 每50帧进行一次延时验证（约0.5秒一次）
                static int delay_check_counter = 0;
                delay_check_counter++;
                if (delay_check_counter >= 50) {
                    delay_check_counter = 0;

                    // 将立体声麦克风信号转换为单声道进行延时估计
                    int16_t mic_mono[ALSA_FRAME_SIZE];
                    for (int i = 0; i < ALSA_FRAME_SIZE; i++) {
                        mic_mono[i] = (alsa_input_buffer[i * 2] + alsa_input_buffer[i * 2 + 1]) / 2;
                    }

                    int estimated_delay = estimate_delay(mic_mono, reference_buffer,
                                                       ALSA_FRAME_SIZE, ALSA_SAMPLE_RATE, 1);
                    if (estimated_delay >= 0) {
                        float estimated_delay_ms = (estimated_delay * 1000.0f) / ALSA_SAMPLE_RATE;
                        float current_delay_ms = g_aec_reference_delay_ms;
                        float delay_diff = fabs(estimated_delay_ms - current_delay_ms);

                        // 添加到自适应调整系统
                        add_delay_estimate(estimated_delay_ms);

                        // 如果估计延时与当前设置差异超过10ms，记录信息
                        if (delay_diff > 10.0f) {
                            LOG_INFO(MODULE_AUDIO_REALTIME,
                                   "📊 AEC delay check: estimated %.1fms vs configured %dms (diff: %.1fms)",
                                   estimated_delay_ms, g_aec_reference_delay_ms, delay_diff);
                        }
                    }
                }
            }
        } else {
            // 不够数据，跳过回声消除
            LOG_DEBUG(MODULE_AUDIO_REALTIME, "Insufficient reference data (need %d, have %d), skipping AEC",
                     total_needed, available_samples);
            memcpy(aec_stereo_buffer, alsa_input_buffer, ALSA_FRAME_SIZE * ALSA_CHANNELS * sizeof(int16_t));
        }
#else
        // 跳过回声消除
        memcpy(aec_stereo_buffer, alsa_input_buffer, ALSA_FRAME_SIZE * ALSA_CHANNELS * sizeof(int16_t));
#endif

        // 1. 执行波束形成（48kHz立体声 -> 48kHz单声道）
        beamforming_process_48khz(processor, aec_stereo_buffer, beamformed_output_buffer, ALSA_FRAME_SIZE);


        // 5. 背景降噪和AGC（SpeexDSP）
        memcpy(speex_processed_buffer, beamformed_output_buffer, ALSA_FRAME_SIZE * sizeof(int16_t));
        speex_preprocess_run(processor->preprocess_state, speex_processed_buffer);

        // 6. 重采样
        spx_uint32_t in_len = ALSA_FRAME_SIZE, out_len = RT_FRAME_SIZE;
        // SpeexDSP 处理输出
        speex_resampler_process_int(processor->resampler_state, 0, speex_processed_buffer, &in_len, resampled_buffer, &out_len);
        // 不处理输出
        //speex_resampler_process_int(processor->resampler_state, 0, beamformed_output_buffer, &in_len, resampled_buffer, &out_len);
        // test loopback
        //speex_resampler_process_int(processor->resampler_state, 0, reference_buffer, &in_len, resampled_buffer, &out_len);

        // 7. VAD处理
        memcpy(speex_output, resampled_buffer, RT_FRAME_SIZE * sizeof(int16_t));
        int speex_vad_result = speex_preprocess_run(processor->vad_state, speex_output);

        // 在预热阶段忽略VAD结果，让算法适应环境噪音
        if (vad_warmup_frames < VAD_WARMUP_COUNT) {
            speex_vad_result = 0;
            vad_warmup_frames++;
        }

        // 3. 检查VAD结果
        processor->last_vad_result = speex_vad_result;
        //if (speex_vad_result) {
        //    processor->last_vad_result = speex_vad_result;
        //}
#if DIALOG_PROCESS_ENABLE
        // 对话处理
        voice_dialog_process_audio(g_voice_dialog, speex_output, RT_FRAME_SIZE, RT_SAMPLE_RATE, RT_CHANNELS_OUT,
                                   speex_vad_result);

#else
        if (speex_vad_result) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "VAD: Speech detected!");
        }
#endif

#if PLAY_ENABLE && PLAY_SAMPLE_RATE == SAMPLE_RATE_48K
        // 4. 48k 为了播放，将处理后的单声道数据复制到左右两个声道
        for (int i = 0; i < ALSA_FRAME_SIZE; i++) {
            //output_buffer[i * 2] = beamformed_output_buffer[i];      // 左声道
            //output_buffer[i * 2 + 1] = beamformed_output_buffer[i];  // 右声道
            output_buffer[i * 2] = speex_processed_buffer[i];      // 左声道
            output_buffer[i * 2 + 1] = speex_processed_buffer[i];  // 右声道
            //output_buffer[i * 2] = reference_buffer[i];      // 左声道
            //output_buffer[i * 2 + 1] = reference_buffer[i];  // 右声道
        }

        // 5. 写入输出缓冲区（48kHz立体声）
        if (ring_buffer_write(rt->output_ring, output_buffer, ALSA_FRAME_SIZE * ALSA_CHANNELS) < 0) {
            printf("Failed to write to output ring buffer\n");
            rt->overruns++;
        }
#endif

#if PLAY_ENABLE && PLAY_SAMPLE_RATE == SAMPLE_RATE_16K
        // 4 使用 16k 单声道播放
        for (int i = 0; i < RT_FRAME_SIZE; i++) {
            output_buffer[i * 2] = speex_output[i];      // 左声道
            output_buffer[i * 2 + 1] = speex_output[i];  // 右声道
        }

        // 5. 写入输出缓冲区（48kHz立体声）
        if (ring_buffer_write(rt->output_ring, output_buffer, RT_FRAME_SIZE * 2) < 0) {
            printf("Failed to write to output ring buffer\n");
            rt->overruns++;
        }
#endif
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Process thread stopped");
    return NULL;
}


/**
 * 初始化实时音频处理器
 */
int realtime_audio_init(RealtimeAudioProcessor *rt_processor,
                        const char *capture_device, const char *playback_device) {
    if (!rt_processor) return -1;

    memset(rt_processor, 0, sizeof(RealtimeAudioProcessor));

    // 设置设备名称
    rt_processor->capture_device = strdup(capture_device ? capture_device : "default");
    rt_processor->playback_device = strdup(playback_device ? playback_device : "default");

    LOG_INFO(MODULE_AUDIO_REALTIME, "Initializing realtime audio processor...");
    LOG_INFO(MODULE_AUDIO_REALTIME, "Capture device: %s", rt_processor->capture_device);
    LOG_INFO(MODULE_AUDIO_REALTIME, "Playback device: %s (DISABLED - API mode)", rt_processor->playback_device);
    LOG_INFO(MODULE_AUDIO_REALTIME, "🎤 2-channel stereo microphone with selective processing");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔊 Enhanced gain: 12x amplification for better sensitivity");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔇 Echo cancellation and noise processing DISABLED");
    LOG_INFO(MODULE_AUDIO_REALTIME, "✅ VAD (Voice Activity Detection) ENABLED");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 Speech recognition and TTS enabled");

    // 初始化应用重采样器（48kHz 2声道 → 16kHz 2声道）
    if (app_resampler_wrapper_init(&g_resampler, 48000, 16000, 2, 8) != 0) {
        fprintf(stderr, "Failed to initialize app resampler");
        realtime_audio_cleanup(rt_processor);
        return -1;
    }

    // 初始化音频处理器（用于波束形成）
    AudioProcessor *processor = malloc(sizeof(AudioProcessor));
    if (!processor) {
        fprintf(stderr, "Failed to allocate audio processor");
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
    memset(processor, 0, sizeof(AudioProcessor));

    // 初始化SpeexDSP处理器（启用回声消除和噪音处理）
    if (speex_processor_init(processor) != 0) {
        fprintf(stderr, "Failed to initialize SpeexDSP processor\n");
        free(processor);
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
    LOG_INFO(MODULE_AUDIO_REALTIME,
             "✅ SpeexDSP processing enabled - Echo cancellation + Noise suppression + AGC + VAD");

    // 初始化波束形成处理器（2声道增强处理）
    // 参数：麦克风间距(米)，目标角度(度，0度为正前方)
    float mic_spacing = 0.05f;  // 5cm麦克风间距（典型值）
    float target_angle = 0.0f;  // 目标方向：正前方
    if (beamforming_init(processor, mic_spacing, target_angle) != 0) {
        fprintf(stderr, "Failed to initialize beamforming processor\n");
        speex_processor_cleanup(processor);
        free(processor);
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
    LOG_INFO(MODULE_AUDIO_REALTIME,
             "✅ Beamforming initialized - 2-channel spatial audio processing (spacing: %.2fm, angle: %.1f°)",
             mic_spacing, target_angle);

    // 保存处理器指针
    rt_processor->audio_processor = processor;
    LOG_INFO(MODULE_AUDIO_REALTIME, "Audio processor and beamforming initialized");


    // 初始化语音对话处理器
    if (!g_voice_dialog) {
        g_voice_dialog = voice_dialog_create();
        if (!g_voice_dialog) {
            fprintf(stderr, "Failed to create voice dialog processor");
            realtime_audio_cleanup(rt_processor);
            return -1;
        }

        // 初始化语音事件处理器（默认启用Ali ASR）
        if (voice_events_init(false) != 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ Failed to initialize voice events handler");
            realtime_audio_cleanup(rt_processor);
            return -1;
        }

        // 设置语音事件回调
        if (voice_events_setup_callbacks(g_voice_dialog) != 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ Failed to setup voice events callbacks");
            voice_events_cleanup();
            realtime_audio_cleanup(rt_processor);
            return -1;
        }

        LOG_INFO(MODULE_AUDIO_REALTIME, "Voice dialog processor initialized");

        // 调用onReady回调，表示系统就绪
        if (g_voice_dialog->on_ready) {
            g_voice_dialog->on_ready();
        }
    }

    // 创建循环缓冲区 (输入和输出都是48kHz立体声)
    rt_processor->input_ring = ring_buffer_create(
            ALSA_FRAME_SIZE * ALSA_CHANNELS * RT_BUFFER_FRAMES * 2);  // 48kHz立体声输入
    rt_processor->output_ring = ring_buffer_create(
            ALSA_FRAME_SIZE * ALSA_CHANNELS * RT_BUFFER_FRAMES * 2); // 48kHz立体声输入

#if ENABLE_AEC_PLAYBACK_CAPTURE
    rt_processor->playback_capture_ring = ring_buffer_create(
            PLAY_FRAME_SIZE * PLAY_CHANNELS * RT_BUFFER_FRAMES * 2); // AEC参考缓冲区
    if (!rt_processor->playback_capture_ring) {
        fprintf(stderr, "Failed to create AEC reference ring buffer");
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
#endif

    if (!rt_processor->input_ring || !rt_processor->output_ring) {
        fprintf(stderr, "Failed to create ring buffers");
        realtime_audio_cleanup(rt_processor);
        return -1;
    }

    // 打开ALSA设备
    int err;
    if ((err = snd_pcm_open(&rt_processor->capture_handle, rt_processor->capture_device,
                            SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        print_alsa_error("snd_pcm_open capture", err);
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
#if PLAY_ENABLE
    if ((err = snd_pcm_open(&rt_processor->playback_handle, rt_processor->playback_device,
                            SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        print_alsa_error("snd_pcm_open playback", err);
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
#endif
#if ENABLE_AEC_PLAYBACK_CAPTURE
    // 为AEC打开硬件音频输出监听设备
    // 尝试多个可能的设备，按优先级顺序
    const char *aec_devices[] = {
            "pulse",                // PulseAudio 默认设备
            "hw:3,1",              // 保留原来的 Loopback 设备作为备选
            "plughw:0,0",          // 主音频设备（带插件支持）
            "hw:0,0",              // 主音频设备
            "default",             // 默认设备
            NULL
    };

    int aec_device_opened = 0;
    for (int i = 0; aec_devices[i] != NULL; i++) {
        if ((err = snd_pcm_open(&rt_processor->aec_capture_handle, aec_devices[i],
                                SND_PCM_STREAM_CAPTURE, 0)) >= 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "AEC hardware output monitor opened: %s", aec_devices[i]);

            // 获取设备的实际配置信息
            snd_pcm_uframes_t period_size, buffer_size;
            unsigned int rate, channels;
            snd_pcm_format_t format;

            if (snd_pcm_get_params(rt_processor->aec_capture_handle, &buffer_size, &period_size) == 0) {
                LOG_INFO(MODULE_AUDIO_REALTIME, "AEC device actual config: buffer_size=%lu, period_size=%lu",
                         buffer_size, period_size);
            }

            aec_device_opened = 1;
            break;
        } else {
            LOG_INFO(MODULE_AUDIO_REALTIME, "Failed to open AEC device %s: %s",
                     aec_devices[i], snd_strerror(err));
        }
    }

    if (!aec_device_opened) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to open any AEC capture device");
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
#endif
    // 打印设备能力信息
    print_device_capabilities(rt_processor->capture_handle, rt_processor->capture_device);
    // 配置ALSA设备
    if (configure_alsa_capture(rt_processor->capture_handle) < 0) {
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
#if PLAY_ENABLE
    print_device_capabilities(rt_processor->playback_handle, rt_processor->playback_device);
    // 配置ALSA设备
    if (configure_alsa_playback(rt_processor->playback_handle) < 0) {
        realtime_audio_cleanup(rt_processor);
        return -1;
    }
#endif
#if ENABLE_AEC_PLAYBACK_CAPTURE
    print_device_capabilities(rt_processor->aec_capture_handle, "AEC Hardware Monitor");
    // 配置AEC硬件监听设备为捕获格式
    if (configure_alsa_capture(rt_processor->aec_capture_handle) < 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Warning: Failed to configure AEC capture device, continuing anyway");
        // 不要返回错误，因为某些设备可能不支持标准配置
    }
#endif


    // 初始化AEC延时缓存（修正：延时不应该乘以声道数）
    g_cached_required_ref_samples =  g_aec_reference_delay_ms * (PLAY_SAMPLE_RATE / 1000);
    g_last_cached_delay_ms = g_aec_reference_delay_ms;
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔧 AEC delay initialized: %d ms (required samples: %d)",
             g_aec_reference_delay_ms, g_cached_required_ref_samples);

    // 初始化并启动HTTP服务器
    if (http_server_init(&g_http_server, HTTP_SERVER_PORT) == 0) {
        if (http_server_start(&g_http_server) == 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "✅ HTTP API server started for AEC delay control");
        } else {
            LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Failed to start HTTP server, continuing without API");
        }
    }

    // 初始化并启动WebSocket服务器
    if (websocket_server_init(&g_websocket_server, WS_SERVER_PORT) == 0) {
        if (websocket_server_start(&g_websocket_server) == 0) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "✅ WebSocket server started for real-time AEC control");
        } else {
            LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Failed to start WebSocket server, continuing without WebSocket");
        }
    }

    rt_processor->initialized = true;
    LOG_INFO(MODULE_AUDIO_REALTIME, "Realtime audio processor initialized successfully");

    return 0;
}

/**
 * 设置线程实时优先级
 */
int set_thread_priority(pthread_t thread, int priority) {
    struct sched_param param;
    int policy;

    // 获取当前调度策略
    pthread_getschedparam(thread, &policy, &param);

    // 设置FIFO实时调度策略
    policy = SCHED_FIFO;
    param.sched_priority = priority;

    if (pthread_setschedparam(thread, policy, &param) != 0) {
        perror("pthread_setschedparam");
        return -1;
    }

    return 0;
}

/**
 * 启动实时音频处理
 */
int realtime_audio_start(RealtimeAudioProcessor *rt_processor) {
    if (!rt_processor || !rt_processor->initialized) {
        return -1;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Starting realtime audio processing...");

    // 尝试锁定内存，防止交换
    if (mlockall(MCL_CURRENT | MCL_FUTURE) < 0) {
        perror("mlockall");
        LOG_INFO(MODULE_AUDIO_REALTIME, "Warning: Failed to lock memory. This may affect real-time performance.");
    }

    rt_processor->running = true;

    // 准备ALSA设备
    snd_pcm_prepare(rt_processor->capture_handle);
#if PLAY_ENABLE
    snd_pcm_prepare(rt_processor->playback_handle);
#endif
#if ENABLE_AEC_PLAYBACK_CAPTURE
    snd_pcm_prepare(rt_processor->aec_capture_handle);
#endif
    // 启动同步采集线程（麦克风 + 扬声器监听）
    if (pthread_create(&rt_processor->capture_thread, NULL,
                       synchronized_capture_thread_func, rt_processor) != 0) {
        fprintf(stderr, "Failed to create synchronized capture thread");
        rt_processor->running = false;
        return -1;
    }

    // 设置同步采集线程高优先级
    if (set_thread_priority(rt_processor->capture_thread, 80) < 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Warning: Failed to set synchronized capture thread priority");
    }
#if PLAY_ENABLE
    if (pthread_create(&rt_processor->playback_thread, NULL,
                       playback_thread_func, rt_processor) != 0) {
        fprintf(stderr, "Failed to create playback thread");
        rt_processor->running = false;
        pthread_join(rt_processor->capture_thread, NULL);
        return -1;
    }

    // 设置播放线程高优先级
    if (set_thread_priority(rt_processor->playback_thread, 75) < 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Warning: Failed to set playback thread priority");
    }
#endif

    if (pthread_create(&rt_processor->process_thread, NULL,
                       process_thread_func, rt_processor) != 0) {
        fprintf(stderr, "Failed to create process thread");
        rt_processor->running = false;
        pthread_join(rt_processor->capture_thread, NULL);
#if PLAY_ENABLE
        pthread_join(rt_processor->playback_thread, NULL);
#endif
        return -1;
    }

    // 设置处理线程中等优先级
    if (set_thread_priority(rt_processor->process_thread, 70) < 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Warning: Failed to set process thread priority");
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Realtime audio processing started");
    return 0;
}

/**
 * 停止实时音频处理
 */
int realtime_audio_stop(RealtimeAudioProcessor *rt_processor) {
    if (!rt_processor || !rt_processor->running) {
        return -1;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Stopping realtime audio processing...");

    rt_processor->running = false;

    // 等待线程结束
    pthread_join(rt_processor->capture_thread, NULL);
#if PLAY_ENABLE
    pthread_join(rt_processor->playback_thread, NULL);
#endif

    pthread_join(rt_processor->process_thread, NULL);

    // 停止ALSA设备
    snd_pcm_drop(rt_processor->capture_handle);
#if PLAY_ENABLE
    snd_pcm_drop(rt_processor->playback_handle);
#endif
#if ENABLE_AEC_PLAYBACK_CAPTURE
    snd_pcm_drop(rt_processor->aec_capture_handle);
#endif

    LOG_INFO(MODULE_AUDIO_REALTIME, "Realtime audio processing stopped");
    return 0;
}

/**
 * 清理实时音频处理器
 */
void realtime_audio_cleanup(RealtimeAudioProcessor *rt_processor) {
    if (!rt_processor) return;

    // 停止处理
    if (rt_processor->running) {
        realtime_audio_stop(rt_processor);
    }

    // 关闭ALSA设备
    if (rt_processor->capture_handle) {
        snd_pcm_close(rt_processor->capture_handle);
        rt_processor->capture_handle = NULL;
    }

    if (rt_processor->playback_handle) {
        snd_pcm_close(rt_processor->playback_handle);
        rt_processor->playback_handle = NULL;
    }

#if ENABLE_AEC_PLAYBACK_CAPTURE
    if (rt_processor->aec_capture_handle) {
        snd_pcm_close(rt_processor->aec_capture_handle);
        rt_processor->aec_capture_handle = NULL;
    }
#endif

    // 清理音频处理器
    if (rt_processor->audio_processor) {
        AudioProcessor *processor = (AudioProcessor *) rt_processor->audio_processor;
        speex_processor_cleanup(processor);
        free(processor);
        rt_processor->audio_processor = NULL;
        LOG_INFO(MODULE_AUDIO_REALTIME, "Audio processor cleaned up");
    }

    // 销毁循环缓冲区
    if (rt_processor->input_ring) {
        ring_buffer_destroy(rt_processor->input_ring);
        rt_processor->input_ring = NULL;
    }

    if (rt_processor->output_ring) {
        ring_buffer_destroy(rt_processor->output_ring);
        rt_processor->output_ring = NULL;
    }

#if ENABLE_AEC_PLAYBACK_CAPTURE
    if (rt_processor->playback_capture_ring) {
        ring_buffer_destroy(rt_processor->playback_capture_ring);
        rt_processor->playback_capture_ring = NULL;
    }
#endif

    // 释放设备名称
    if (rt_processor->capture_device) {
        free(rt_processor->capture_device);
        rt_processor->capture_device = NULL;
    }

    if (rt_processor->playback_device) {
        free(rt_processor->playback_device);
        rt_processor->playback_device = NULL;
    }

    // 清理语音对话处理器
    if (g_voice_dialog) {
        // 调用onClose回调
        if (g_voice_dialog->on_close) {
            g_voice_dialog->on_close();
        }

        // 清理语音事件处理器
        voice_events_cleanup();

        voice_dialog_destroy(g_voice_dialog);
        g_voice_dialog = NULL;
        LOG_INFO(MODULE_AUDIO_REALTIME, "Voice dialog processor cleaned up");
    }

    // 停止WebSocket服务器
    websocket_server_stop(&g_websocket_server);
    websocket_server_cleanup(&g_websocket_server);

    // 停止HTTP服务器
    http_server_cleanup(&g_http_server);

    // 清理应用重采样器
    app_resampler_wrapper_cleanup(&g_resampler);

    rt_processor->initialized = false;
    LOG_INFO(MODULE_AUDIO_REALTIME, "Realtime audio processor cleaned up");
}

/**
 * 打印实时处理统计信息
 */
void print_realtime_stats(const RealtimeAudioProcessor *rt_processor) {
    if (!rt_processor) return;

    LOG_INFO(MODULE_AUDIO_REALTIME, "\n=== Realtime Audio Statistics ===");
    LOG_INFO(MODULE_AUDIO_REALTIME, "Frames captured: %lu", rt_processor->frames_captured);
    LOG_INFO(MODULE_AUDIO_REALTIME, "Frames processed: %lu", rt_processor->frames_processed);
    LOG_INFO(MODULE_AUDIO_REALTIME, "Frames played: %lu", rt_processor->frames_played);
    LOG_INFO(MODULE_AUDIO_REALTIME, "Underruns: %lu", rt_processor->underruns);
    LOG_INFO(MODULE_AUDIO_REALTIME, "Overruns: %lu", rt_processor->overruns);

    if (rt_processor->input_ring) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Input buffer: %d/%d samples",
                 ring_buffer_available_read(rt_processor->input_ring),
                 rt_processor->input_ring->size);
    }

    if (rt_processor->output_ring) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "Output buffer: %d/%d samples",
                 ring_buffer_available_read(rt_processor->output_ring),
                 rt_processor->output_ring->size);
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "================================");
}
