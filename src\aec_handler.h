#ifndef AEC_HANDLER_H
#define AEC_HANDLER_H

#include <stdint.h>
#include <pthread.h>
#include <libwebsockets.h>

// AEC处理相关常量
#define AEC_SAMPLE_RATE 48000
#define AEC_FRAME_SIZE 480      // 10ms @ 48kHz
//#define AEC_TAIL_LENGTH 14400   // 300ms @ 48kHz
#define AEC_TAIL_LENGTH 4800*2   // 200ms @ 48kHz
#define MAX_AUDIO_DATA_SIZE (48000 * 120 * 2)  // 最大120秒音频数据（约11MB）

// 前向声明
typedef struct aec_processing_context aec_processing_context_t;

// AEC处理上下文结构
struct aec_processing_context {
    int16_t *mic_data;
    int16_t *ref_data;
    int16_t *output_data;
    int mic_samples;
    int ref_samples;
    int output_samples;
    int progress;
    int total_frames;
    int is_processing;
    pthread_t processing_thread;
    struct lws *client_wsi;

    // 分包接收相关
    char **mic_chunks;  // 指针数组存储各个块
    char **ref_chunks;  // 指针数组存储各个块
    int *mic_chunk_sizes;
    int *ref_chunk_sizes;
    int mic_expected_chunks;
    int ref_expected_chunks;
    int mic_received_chunks;
    int ref_received_chunks;
    int mic_samples_expected;
    int ref_samples_expected;
    int mic_upload_complete;
    int ref_upload_complete;
};

// AEC处理函数声明
void aec_handler_init(void);
void aec_handler_cleanup(void);
void aec_cleanup_context(void);

// 消息处理函数
int aec_handle_start_mic_upload(const char *message, char *response, size_t response_size);
int aec_handle_upload_mic_chunk(const char *message, char *response, size_t response_size);
int aec_handle_start_ref_upload(const char *message, char *response, size_t response_size);
int aec_handle_upload_ref_chunk(const char *message, char *response, size_t response_size);
int aec_handle_start_processing(const char *message, char *response, size_t response_size);
int aec_handle_stop_processing(const char *message, char *response, size_t response_size);
int aec_handle_get_status(const char *message, char *response, size_t response_size);
int aec_handle_get_result(const char *message, char *response, size_t response_size);

// 广播函数
void aec_broadcast_progress(int progress, int total_frames);
void aec_broadcast_complete_with_data(const char *output_data, int samples, int success);

// 获取AEC上下文（用于其他模块访问）
aec_processing_context_t* aec_get_context(void);

// 获取处理结果数据
int aec_get_output_data(char **output_data, int *samples);

// 获取WAV格式的输出数据
int aec_get_output_wav(unsigned char **wav_data, int *wav_size);

// 从文件加载音频数据
int aec_load_mic_file(const char *filename);
int aec_load_ref_file(const char *filename);

// 内部辅助函数
void aec_process_mic_complete(char *response, size_t response_size);
void aec_process_ref_complete(char *response, size_t response_size);

#endif // AEC_HANDLER_H
