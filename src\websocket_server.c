#include "websocket_server.h"
#include "aec_handler.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>
#include <unistd.h>
#include <time.h>

// 全局WebSocket上下文，用于广播消息
static struct lws_context *g_ws_context = NULL;
static struct lws *g_connected_clients[10]; // 最多支持10个客户端
static int g_client_count = 0;

// 音频流控制
static int g_audio_streaming_enabled = 0;  // 音频流是否启用
static int g_audio_stream_clients = 0;     // 请求音频流的客户端数量

// 外部变量声明
extern int g_auto_adjust_enabled;          // 自动调整是否启用





/**
 * Base64解码函数
 */
static int base64_decode(const char *input, unsigned char *output, int max_output_len) {
    static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON>ijklmnopqrstuvwxyz0123456789+/";
    int input_len = strlen(input);
    int output_len = 0;
    int i, j;
    unsigned char char_array_4[4], char_array_3[3];

    for (i = 0; i < input_len && output_len < max_output_len; ) {
        for (j = 0; j < 4 && i < input_len; j++, i++) {
            char *p = strchr(base64_chars, input[i]);
            if (p) {
                char_array_4[j] = p - base64_chars;
            } else if (input[i] == '=') {
                char_array_4[j] = 0;
            } else {
                continue; // 跳过无效字符
            }
        }

        if (j >= 2) {
            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            if (output_len < max_output_len) output[output_len++] = char_array_3[0];

            if (j >= 3) {
                char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
                if (output_len < max_output_len) output[output_len++] = char_array_3[1];

                if (j >= 4) {
                    char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
                    if (output_len < max_output_len) output[output_len++] = char_array_3[2];
                }
            }
        }
    }

    return output_len;
}

/**
 * Base64编码函数
 */
static int base64_encode(const unsigned char *input, int input_len, char *output, int max_output_len) {
    static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    int output_len = 0;
    int i;
    unsigned char char_array_3[3], char_array_4[4];

    for (i = 0; i < input_len && output_len < max_output_len - 4; ) {
        int j;
        for (j = 0; j < 3 && i < input_len; j++, i++) {
            char_array_3[j] = input[i];
        }

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
        char_array_4[3] = char_array_3[2] & 0x3f;

        for (int k = 0; k < 4 && output_len < max_output_len - 1; k++) {
            if (k < j + 1) {
                output[output_len++] = base64_chars[char_array_4[k]];
            } else {
                output[output_len++] = '=';
            }
        }
    }

    output[output_len] = '\0';
    return output_len;
}

// 前向声明
void *aec_processing_thread(void *arg);

/**
 * WebSocket协议回调函数 - 处理AEC控制
 */
int callback_aec_control(struct lws *wsi, enum lws_callback_reasons reason,
                        void *user __attribute__((unused)), void *in, size_t len) {
    static char response[4096]; // 使用较小的静态缓冲区用于响应
    unsigned char *buf = NULL;
    unsigned char *p = NULL;
    int response_len;

    switch (reason) {
        case LWS_CALLBACK_ESTABLISHED:
            // 新客户端连接
            if (g_client_count < 10) {
                g_connected_clients[g_client_count++] = wsi;
                LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 WebSocket client connected (total: %d)", g_client_count);

                // 清理AEC上下文，为新连接准备
                aec_cleanup_context();

                // 发送当前AEC延时状态
                snprintf(response, sizeof(response),
                        "{\"type\":\"aec_delay_notify\",\"delay_ms\":%d,\"status\":\"connected\"}",
                        g_aec_reference_delay_ms);
                response_len = strlen(response);

                // 动态分配发送缓冲区
                buf = malloc(LWS_PRE + response_len);
                if (buf) {
                    p = &buf[LWS_PRE];
                    memcpy(p, response, response_len);
                    lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
                    free(buf);
                }
            }
            break;

        case LWS_CALLBACK_CLOSED:
            // 客户端断开连接
            for (int i = 0; i < g_client_count; i++) {
                if (g_connected_clients[i] == wsi) {
                    // 移除断开的客户端
                    for (int j = i; j < g_client_count - 1; j++) {
                        g_connected_clients[j] = g_connected_clients[j + 1];
                    }
                    g_client_count--;

                    // 清理AEC上下文
                    aec_cleanup_context();

                    // 如果这个客户端正在接收音频流，减少计数
                    if (g_audio_stream_clients > 0) {
                        g_audio_stream_clients--;
                        if (g_audio_stream_clients == 0) {
                            g_audio_streaming_enabled = 0;
                            LOG_INFO(MODULE_AUDIO_REALTIME, "🔇 Audio streaming stopped (client disconnected)");
                        }
                    }
                    break;
                }
            }
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ WebSocket client disconnected (total: %d, streaming clients: %d)",
                     g_client_count, g_audio_stream_clients);
            break;

        case LWS_CALLBACK_RECEIVE:
            // 接收到客户端消息
            {
                char *message = (char *)in;
                message[len] = '\0'; // 确保字符串结束
                
                //LOG_INFO(MODULE_AUDIO_REALTIME, "📨 WebSocket received: %s", message);
                
                // 解析JSON消息
                json_object *root = json_tokener_parse(message);
                if (root != NULL) {
                    json_object *type_obj, *data_obj;
                    
                    if (json_object_object_get_ex(root, "type", &type_obj)) {
                        const char *msg_type = json_object_get_string(type_obj);
                        
                        if (strcmp(msg_type, "get_aec_delay") == 0) {
                            // 获取当前AEC延时
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"aec_delay_response\",\"delay_ms\":%d,\"status\":\"success\"}",
                                    g_aec_reference_delay_ms);
                            
                        } else if (strcmp(msg_type, "set_aec_delay") == 0) {
                            // 设置AEC延时
                            if (json_object_object_get_ex(root, "delay_ms", &data_obj)) {
                                int new_delay = json_object_get_int(data_obj);
                                
                                if (new_delay >= 1 && new_delay <= 200) {
                                    int old_delay = g_aec_reference_delay_ms;
                                    g_aec_reference_delay_ms = new_delay;
                                    
                                    LOG_INFO(MODULE_AUDIO_REALTIME, 
                                            "🔄 WebSocket AEC delay changed: %d ms → %d ms", 
                                            old_delay, new_delay);
                                    
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"aec_delay_response\",\"old_delay_ms\":%d,\"new_delay_ms\":%d,\"status\":\"success\"}",
                                            old_delay, new_delay);
                                    
                                    // 广播变化到所有客户端
                                    websocket_broadcast_aec_delay_change(old_delay, new_delay);
                                } else {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"aec_delay_response\",\"status\":\"error\",\"message\":\"Invalid delay range (5-200ms)\"}");
                                }
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_delay_response\",\"status\":\"error\",\"message\":\"Missing delay_ms parameter\"}");
                            }
                            
                        } else if (strcmp(msg_type, "get_auto_adjust") == 0) {
                            // 获取自动调整状态
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"auto_adjust_response\",\"enabled\":%s,\"status\":\"success\"}",
                                    g_auto_adjust_enabled ? "true" : "false");

                        } else if (strcmp(msg_type, "set_auto_adjust") == 0) {
                            // 设置自动调整
                            if (json_object_object_get_ex(root, "enabled", &data_obj)) {
                                int new_enabled = json_object_get_boolean(data_obj);
                                int old_enabled = g_auto_adjust_enabled;
                                g_auto_adjust_enabled = new_enabled;

                                LOG_INFO(MODULE_AUDIO_REALTIME,
                                        "🤖 WebSocket auto-adjust changed: %s → %s",
                                        old_enabled ? "enabled" : "disabled",
                                        new_enabled ? "enabled" : "disabled");

                                snprintf(response, sizeof(response),
                                        "{\"type\":\"auto_adjust_response\",\"old_enabled\":%s,\"new_enabled\":%s,\"status\":\"success\"}",
                                        old_enabled ? "true" : "false",
                                        new_enabled ? "true" : "false");

                                // 广播变化到所有客户端
                                websocket_broadcast_auto_adjust_change(old_enabled, new_enabled);
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"auto_adjust_response\",\"status\":\"error\",\"message\":\"Missing enabled parameter\"}");
                            }

                        } else if (strcmp(msg_type, "start_mic_upload") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_mic_upload((char*)in, response, sizeof(response));

                        } else if (strcmp(msg_type, "upload_mic_chunk") == 0) {
                            // 接收麦克风数据块
                            json_object *chunk_index_obj, *chunk_data_obj;

                            if (json_object_object_get_ex(root, "chunk_index", &chunk_index_obj) &&
                                json_object_object_get_ex(root, "chunk_data", &chunk_data_obj)) {

                                int chunk_index = json_object_get_int(chunk_index_obj);
                                const char *chunk_data = json_object_get_string(chunk_data_obj);
                                int chunk_size = chunk_data ? strlen(chunk_data) : 0;

                                LOG_INFO(MODULE_AUDIO_REALTIME, "Receiving mic chunk %d/%d, size: %d",
                                        chunk_index, g_aec_context.mic_expected_chunks, chunk_size);

                                if (!g_aec_context.mic_chunks) {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Upload not initialized\"}");
                                } else if (chunk_index < 0 || chunk_index >= g_aec_context.mic_expected_chunks) {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk index\"}");
                                } else if (chunk_size <= 0 || chunk_size > 65536) {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk size\"}");
                                } else if (g_aec_context.mic_chunks[chunk_index] != NULL) {
                                    // 重复的块，直接返回成功
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                                            chunk_index, g_aec_context.mic_received_chunks, g_aec_context.mic_expected_chunks);
                                } else {

                                    // 存储这个数据块
                                    g_aec_context.mic_chunks[chunk_index] = malloc(chunk_size + 1);
                                    if (!g_aec_context.mic_chunks[chunk_index]) {
                                        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate memory for mic chunk %d", chunk_index);
                                        snprintf(response, sizeof(response),
                                                "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
                                    } else {
                                        memcpy(g_aec_context.mic_chunks[chunk_index], chunk_data, chunk_size);
                                        g_aec_context.mic_chunks[chunk_index][chunk_size] = '\0';
                                        g_aec_context.mic_chunk_sizes[chunk_index] = chunk_size;
                                        g_aec_context.mic_received_chunks++;

                                        snprintf(response, sizeof(response),
                                                "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                                                chunk_index, g_aec_context.mic_received_chunks, g_aec_context.mic_expected_chunks);

                                        // 检查是否接收完所有块
                                        if (g_aec_context.mic_received_chunks >= g_aec_context.mic_expected_chunks) {
                                            LOG_INFO(MODULE_AUDIO_REALTIME, "All mic chunks received, assembling data...");

                                            // 验证所有块都已接收
                                            int missing_chunks = 0;
                                            for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
                                                if (!g_aec_context.mic_chunks[i]) {
                                                    missing_chunks++;
                                                }
                                            }

                                            if (missing_chunks > 0) {
                                                LOG_ERROR(MODULE_AUDIO_REALTIME, "Missing %d mic chunks", missing_chunks);
                                                snprintf(response, sizeof(response),
                                                        "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Missing chunks\"}");
                                            } else {
                                                // 计算总大小并拼接所有块
                                                int total_size = 0;
                                                for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
                                                    total_size += g_aec_context.mic_chunk_sizes[i];
                                                }

                                                LOG_INFO(MODULE_AUDIO_REALTIME, "Assembling %d chunks, total size: %d",
                                                        g_aec_context.mic_expected_chunks, total_size);

                                                char *complete_data = malloc(total_size + 1);
                                                if (!complete_data) {
                                                    LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate complete data buffer");
                                                    snprintf(response, sizeof(response),
                                                            "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
                                                } else {
                                                    int pos = 0;
                                                    for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
                                                        if (pos + g_aec_context.mic_chunk_sizes[i] <= total_size) {
                                                            memcpy(complete_data + pos, g_aec_context.mic_chunks[i], g_aec_context.mic_chunk_sizes[i]);
                                                            pos += g_aec_context.mic_chunk_sizes[i];
                                                        }
                                                    }
                                                    complete_data[total_size] = '\0';

                                                    // 解码完整数据
                                                    g_aec_context.mic_data = malloc(g_aec_context.mic_samples_expected * sizeof(int16_t));
                                                    if (!g_aec_context.mic_data) {
                                                        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate mic data buffer");
                                                        snprintf(response, sizeof(response),
                                                                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
                                                    } else {
                                                        int decoded = base64_decode(complete_data,
                                                                                  (unsigned char*)g_aec_context.mic_data,
                                                                                  g_aec_context.mic_samples_expected * sizeof(int16_t));

                                                        LOG_INFO(MODULE_AUDIO_REALTIME, "Decoded %d bytes, expected %d",
                                                                decoded, (int)(g_aec_context.mic_samples_expected * sizeof(int16_t)));

                                                        if (decoded == (int)(g_aec_context.mic_samples_expected * sizeof(int16_t))) {
                                                            g_aec_context.mic_samples = g_aec_context.mic_samples_expected;
                                                            g_aec_context.mic_upload_complete = 1;
                                                            LOG_INFO(MODULE_AUDIO_REALTIME, "Mic upload completed successfully");
                                                            snprintf(response, sizeof(response),
                                                                    "{\"type\":\"upload_complete\",\"status\":\"success\",\"upload_type\":\"mic\",\"samples\":%d}",
                                                                    g_aec_context.mic_samples);
                                                        } else {
                                                            LOG_ERROR(MODULE_AUDIO_REALTIME, "Base64 decode failed");
                                                            snprintf(response, sizeof(response),
                                                                    "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Decode failed\"}");
                                                        }
                                                    }
                                                    free(complete_data);
                                                }
                                            }

                                            // 清理临时块
                                            for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
                                                if (g_aec_context.mic_chunks[i]) {
                                                    free(g_aec_context.mic_chunks[i]);
                                                    g_aec_context.mic_chunks[i] = NULL;
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Missing chunk data\"}");
                            }

                        } else if (strcmp(msg_type, "start_ref_upload") == 0) {
                            // 开始参考信号数据分包上传
                            json_object *total_chunks_obj, *samples_obj;

                            if (json_object_object_get_ex(root, "total_chunks", &total_chunks_obj) &&
                                json_object_object_get_ex(root, "samples", &samples_obj)) {

                                int total_chunks = json_object_get_int(total_chunks_obj);
                                int samples = json_object_get_int(samples_obj);

                                // 验证参数
                                if (total_chunks <= 0 || total_chunks > 1000 || samples <= 0 || samples > MAX_AUDIO_DATA_SIZE) {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Invalid parameters\"}");
                                } else {
                                    // 清理之前的数据
                                    if (g_aec_context.ref_chunks) {
                                        for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
                                            if (g_aec_context.ref_chunks[i]) free(g_aec_context.ref_chunks[i]);
                                        }
                                        free(g_aec_context.ref_chunks);
                                        free(g_aec_context.ref_chunk_sizes);
                                    }
                                    if (g_aec_context.ref_data) {
                                        free(g_aec_context.ref_data);
                                        g_aec_context.ref_data = NULL;
                                    }

                                    // 分配块指针数组
                                    g_aec_context.ref_chunks = calloc(total_chunks, sizeof(char*));
                                    g_aec_context.ref_chunk_sizes = calloc(total_chunks, sizeof(int));

                                    if (g_aec_context.ref_chunks && g_aec_context.ref_chunk_sizes) {
                                        g_aec_context.ref_expected_chunks = total_chunks;
                                        g_aec_context.ref_received_chunks = 0;
                                        g_aec_context.ref_samples_expected = samples;
                                        g_aec_context.ref_upload_complete = 0;

                                        snprintf(response, sizeof(response),
                                                "{\"type\":\"start_upload_response\",\"status\":\"success\",\"upload_type\":\"ref\"}");
                                    } else {
                                        snprintf(response, sizeof(response),
                                                "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
                                    }
                                }
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Missing parameters\"}");
                            }

                        } else if (strcmp(msg_type, "upload_ref_chunk") == 0) {
                            // 接收参考信号数据块
                            json_object *chunk_index_obj, *chunk_data_obj;

                            if (json_object_object_get_ex(root, "chunk_index", &chunk_index_obj) &&
                                json_object_object_get_ex(root, "chunk_data", &chunk_data_obj)) {

                                int chunk_index = json_object_get_int(chunk_index_obj);
                                const char *chunk_data = json_object_get_string(chunk_data_obj);
                                int chunk_size = strlen(chunk_data);

                                if (g_aec_context.ref_chunks && chunk_index >= 0 &&
                                    chunk_index < g_aec_context.ref_expected_chunks && chunk_size > 0) {

                                    // 存储这个数据块
                                    g_aec_context.ref_chunks[chunk_index] = malloc(chunk_size + 1);
                                    if (g_aec_context.ref_chunks[chunk_index]) {
                                        memcpy(g_aec_context.ref_chunks[chunk_index], chunk_data, chunk_size);
                                        g_aec_context.ref_chunks[chunk_index][chunk_size] = '\0';
                                        g_aec_context.ref_chunk_sizes[chunk_index] = chunk_size;
                                        g_aec_context.ref_received_chunks++;

                                        snprintf(response, sizeof(response),
                                                "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                                                chunk_index, g_aec_context.ref_received_chunks, g_aec_context.ref_expected_chunks);

                                        // 检查是否接收完所有块
                                        if (g_aec_context.ref_received_chunks >= g_aec_context.ref_expected_chunks) {
                                            // 计算总大小并拼接所有块
                                            int total_size = 0;
                                            for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
                                                total_size += g_aec_context.ref_chunk_sizes[i];
                                            }

                                            char *complete_data = malloc(total_size + 1);
                                            if (complete_data) {
                                                int pos = 0;
                                                for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
                                                    memcpy(complete_data + pos, g_aec_context.ref_chunks[i], g_aec_context.ref_chunk_sizes[i]);
                                                    pos += g_aec_context.ref_chunk_sizes[i];
                                                }
                                                complete_data[total_size] = '\0';

                                                // 解码完整数据
                                                g_aec_context.ref_data = malloc(g_aec_context.ref_samples_expected * sizeof(int16_t));
                                                if (g_aec_context.ref_data) {
                                                    int decoded = base64_decode(complete_data,
                                                                              (unsigned char*)g_aec_context.ref_data,
                                                                              g_aec_context.ref_samples_expected * sizeof(int16_t));

                                                    if (decoded == (int)(g_aec_context.ref_samples_expected * sizeof(int16_t))) {
                                                        g_aec_context.ref_samples = g_aec_context.ref_samples_expected;
                                                        g_aec_context.ref_upload_complete = 1;
                                                        snprintf(response, sizeof(response),
                                                                "{\"type\":\"upload_complete\",\"status\":\"success\",\"upload_type\":\"ref\",\"samples\":%d}",
                                                                g_aec_context.ref_samples);
                                                    } else {
                                                        snprintf(response, sizeof(response),
                                                                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Decode failed\"}");
                                                    }
                                                }
                                                free(complete_data);
                                            }

                                            // 清理临时块
                                            for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
                                                if (g_aec_context.ref_chunks[i]) {
                                                    free(g_aec_context.ref_chunks[i]);
                                                    g_aec_context.ref_chunks[i] = NULL;
                                                }
                                            }
                                        }
                                    } else {
                                        snprintf(response, sizeof(response),
                                                "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
                                    }
                                } else {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk or not initialized\"}");
                                }
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Missing chunk data\"}");
                            }

                        } else if (strcmp(msg_type, "start_aec_processing") == 0) {
                            // 开始AEC处理
                            if (g_aec_context.is_processing) {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_response\",\"status\":\"error\",\"message\":\"AEC processing already in progress\"}");
                            } else if (!g_aec_context.mic_upload_complete || !g_aec_context.ref_upload_complete ||
                                      !g_aec_context.mic_data || !g_aec_context.ref_data ||
                                      g_aec_context.mic_samples <= 0 || g_aec_context.ref_samples <= 0) {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_response\",\"status\":\"error\",\"message\":\"Audio data upload not complete\"}");
                            } else {
                                // 计算总帧数
                                g_aec_context.total_frames = (g_aec_context.mic_samples + AEC_FRAME_SIZE - 1) / AEC_FRAME_SIZE;

                                g_aec_context.is_processing = 1;
                                g_aec_context.progress = 0;
                                g_aec_context.client_wsi = wsi;

                                // 启动处理线程
                                if (pthread_create(&g_aec_context.processing_thread, NULL,
                                                 aec_processing_thread, &g_aec_context) == 0) {
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"aec_response\",\"status\":\"started\",\"total_frames\":%d,\"mic_samples\":%d,\"ref_samples\":%d}",
                                            g_aec_context.total_frames, g_aec_context.mic_samples, g_aec_context.ref_samples);
                                } else {
                                    g_aec_context.is_processing = 0;
                                    snprintf(response, sizeof(response),
                                            "{\"type\":\"aec_response\",\"status\":\"error\",\"message\":\"Failed to start processing thread\"}");
                                }
                            }

                        } else if (strcmp(msg_type, "stop_aec_processing") == 0) {
                            // 停止AEC处理
                            if (g_aec_context.is_processing) {
                                g_aec_context.is_processing = 0;
                                pthread_join(g_aec_context.processing_thread, NULL);
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_response\",\"status\":\"stopped\"}");
                            } else {
                                snprintf(response, sizeof(response),
                                        "{\"type\":\"aec_response\",\"status\":\"not_running\"}");
                            }

                        } else if (strcmp(msg_type, "get_aec_status") == 0) {
                            // 获取AEC处理状态
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"aec_status_response\",\"is_processing\":%s,\"progress\":%d,\"total_frames\":%d}",
                                    g_aec_context.is_processing ? "true" : "false",
                                    g_aec_context.progress, g_aec_context.total_frames);

                        } else if (strcmp(msg_type, "get_audio_status") == 0) {
                            // 获取音频状态
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_status_response\",\"status\":\"running\",\"aec_delay_ms\":%d,\"auto_adjust_enabled\":%s,\"audio_streaming\":%s,\"aec_processing\":%s}",
                                    g_aec_reference_delay_ms, g_auto_adjust_enabled ? "true" : "false",
                                    g_audio_streaming_enabled ? "true" : "false",
                                    g_aec_context.is_processing ? "true" : "false");

                        } else if (strcmp(msg_type, "start_audio_stream") == 0) {
                            // 开始音频流
                            g_audio_stream_clients++;
                            if (g_audio_stream_clients == 1) {
                                g_audio_streaming_enabled = 1;
                                LOG_INFO(MODULE_AUDIO_REALTIME, "🎵 Audio streaming started (enabled=%d)", g_audio_streaming_enabled);
                            }
                            LOG_INFO(MODULE_AUDIO_REALTIME, "📡 Client requested audio stream (total clients: %d)", g_audio_stream_clients);
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_stream_response\",\"status\":\"started\",\"clients\":%d}",
                                    g_audio_stream_clients);

                        } else if (strcmp(msg_type, "stop_audio_stream") == 0) {
                            // 停止音频流
                            if (g_audio_stream_clients > 0) {
                                g_audio_stream_clients--;
                                if (g_audio_stream_clients == 0) {
                                    g_audio_streaming_enabled = 0;
                                    LOG_INFO(MODULE_AUDIO_REALTIME, "🔇 Audio streaming stopped");
                                }
                            }
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"audio_stream_response\",\"status\":\"stopped\",\"clients\":%d}",
                                    g_audio_stream_clients);

                        } else {
                            snprintf(response, sizeof(response),
                                    "{\"type\":\"error\",\"message\":\"Unknown message type: %s\"}", msg_type);
                        }
                    } else {
                        snprintf(response, sizeof(response),
                                "{\"type\":\"error\",\"message\":\"Missing type field\"}");
                    }
                    
                    json_object_put(root);
                } else {
                    snprintf(response, sizeof(response),
                            "{\"type\":\"error\",\"message\":\"Invalid JSON format\"}");
                }
                
                // 发送响应
                response_len = strlen(response);

                // 动态分配发送缓冲区
                buf = malloc(LWS_PRE + response_len);
                if (buf) {
                    p = &buf[LWS_PRE];
                    memcpy(p, response, response_len);
                    lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
                    free(buf);
                } else {
                    LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate send buffer");
                }
            }
            break;

        default:
            break;
    }
    return 0;
}

// WebSocket协议定义
static struct lws_protocols protocols[] = {
    {
        .name = "aec-control-protocol",
        .callback = callback_aec_control,
        .per_session_data_size = 0,
        .rx_buffer_size = 1024 * 1024, // 1MB接收缓冲区
    },
    { NULL, NULL, 0, 0, 0, NULL, 0 } // 必须以空终结
};

/**
 * WebSocket服务器线程函数
 */
void *websocket_server_thread(void *arg) {
    WebSocketServer *server = (WebSocketServer *)arg;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "🚀 WebSocket server thread started");
    
    while (server->running) {
        lws_service(server->context, 1000); // 1秒超时
    }
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "🛑 WebSocket server thread stopped");
    return NULL;
}

/**
 * 初始化WebSocket服务器
 */
int websocket_server_init(WebSocketServer *server, int port) {
    if (!server) return -1;
    
    memset(server, 0, sizeof(WebSocketServer));
    server->port = port;
    server->running = 0;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server initialized on port %d", port);
    return 0;
}

/**
 * 启动WebSocket服务器
 */
int websocket_server_start(WebSocketServer *server) {
    if (!server || server->running) return -1;
    
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    info.port = server->port;
    info.protocols = protocols;
    info.gid = -1;
    info.uid = -1;
    info.max_http_header_data = MAX_WS_MESSAGE_SIZE;
    info.max_http_header_pool = 16;
    info.options = LWS_SERVER_OPTION_VALIDATE_UTF8;
    
    server->context = lws_create_context(&info);
    if (!server->context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket context on port %d", server->port);
        return -1;
    }
    
    g_ws_context = server->context; // 保存全局上下文用于广播
    server->running = 1;
    
    // 创建WebSocket服务线程
    if (pthread_create(&server->thread, NULL, websocket_server_thread, server) != 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket server thread");
        lws_context_destroy(server->context);
        server->context = NULL;
        server->running = 0;
        return -1;
    }
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 WebSocket server started on port %d", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket protocol: aec-control-protocol");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 Connect: ws://localhost:%d", server->port);
    
    return 0;
}

/**
 * 停止WebSocket服务器
 */
void websocket_server_stop(WebSocketServer *server) {
    if (!server || !server->running) return;
    
    server->running = 0;
    
    // 等待线程结束
    if (server->thread) {
        pthread_join(server->thread, NULL);
        server->thread = 0;
    }
    
    // 销毁上下文
    if (server->context) {
        lws_context_destroy(server->context);
        server->context = NULL;
    }
    
    g_ws_context = NULL;
    g_client_count = 0;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server stopped");
}

/**
 * 清理WebSocket服务器
 */
void websocket_server_cleanup(WebSocketServer *server) {
    if (!server) return;
    
    websocket_server_stop(server);
    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server cleaned up");
}

/**
 * 广播AEC延时变化到所有客户端
 */
void websocket_broadcast_aec_delay_change(int old_delay, int new_delay) {
    if (!g_ws_context || g_client_count == 0) return;
    
    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_delay_notify\",\"old_delay_ms\":%d,\"new_delay_ms\":%d}",
             old_delay, new_delay);
    
    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);
    
    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: AEC delay change to %d clients", g_client_count);
}

/**
 * 广播自动调整状态变化到所有客户端
 */
void websocket_broadcast_auto_adjust_change(int old_enabled, int new_enabled) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"auto_adjust_notify\",\"old_enabled\":%s,\"new_enabled\":%s}",
             old_enabled ? "true" : "false", new_enabled ? "true" : "false");

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: Auto-adjust change to %d clients", g_client_count);
}

/**
 * 广播延时估计信息到所有客户端
 */
void websocket_broadcast_delay_estimate(float estimated_delay_ms, float mic_rms, float ref_rms,
                                       float correlation, int current_delay_ms) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"delay_estimate_notify\",\"estimated_delay_ms\":%.1f,\"mic_rms\":%.1f,\"ref_rms\":%.1f,\"correlation\":%.3f,\"current_delay_ms\":%d,\"timestamp\":%ld}",
             estimated_delay_ms, mic_rms, ref_rms, correlation, current_delay_ms, time(NULL));

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    LOG_DEBUG(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: Delay estimate to %d clients", g_client_count);
}

/**
 * 广播信号强度信息到所有客户端
 */
void websocket_broadcast_signal_strength(float mic_rms, float ref_rms, const char* status) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"signal_strength_notify\",\"mic_rms\":%.1f,\"ref_rms\":%.1f,\"status\":\"%s\",\"timestamp\":%ld}",
             mic_rms, ref_rms, status, time(NULL));

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    LOG_DEBUG(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: Signal strength to %d clients", g_client_count);
}

/**
 * 创建目录（如果不存在）
 */
int create_directory(const char *path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == -1) {
            LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create directory %s: %s", path, strerror(errno));
            return -1;
        }
    }
    return 0;
}

/**
 * 广播AEC处理进度
 */
void websocket_broadcast_aec_progress(int progress, int total_frames) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    float percentage = total_frames > 0 ? (float)progress * 100.0f / total_frames : 0.0f;
    float seconds = (float)progress * AEC_FRAME_SIZE / AEC_SAMPLE_RATE;

    snprintf(message, sizeof(message),
             "{\"type\":\"aec_progress\",\"progress\":%d,\"total\":%d,\"percentage\":%.1f,\"seconds\":%.2f}",
             progress, total_frames, percentage, seconds);

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }
}

/**
 * 广播AEC处理完成（包含数据）
 */
void websocket_broadcast_aec_complete_with_data(const char *output_data, int samples, int success) {
    if (!g_ws_context || g_client_count == 0) return;

    // 由于数据可能很大，我们需要分块发送或使用更大的缓冲区
    // 这里先发送完成通知，然后单独发送数据
    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_complete\",\"success\":%s,\"samples\":%d,\"has_data\":%s}",
             success ? "true" : "false", samples, output_data ? "true" : "false");

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播完成通知到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    // 如果有数据，单独发送数据
    if (success && output_data && samples > 0) {
        // 分块发送大数据
        const int chunk_size = 32768; // 32KB chunks
        int data_len = strlen(output_data);
        int chunks = (data_len + chunk_size - 1) / chunk_size;

        for (int chunk = 0; chunk < chunks; chunk++) {
            int start = chunk * chunk_size;
            int end = (start + chunk_size < data_len) ? start + chunk_size : data_len;
            int current_chunk_size = end - start;

            // 创建数据块消息
            char *chunk_message = malloc(current_chunk_size + 200);
            if (chunk_message) {
                snprintf(chunk_message, current_chunk_size + 200,
                        "{\"type\":\"aec_data_chunk\",\"chunk\":%d,\"total_chunks\":%d,\"data\":\"",
                        chunk, chunks);

                int header_len = strlen(chunk_message);
                memcpy(chunk_message + header_len, output_data + start, current_chunk_size);
                strcpy(chunk_message + header_len + current_chunk_size, "\"}");

                int total_len = header_len + current_chunk_size + 2;

                // 发送数据块
                unsigned char *chunk_buf = malloc(LWS_PRE + total_len);
                if (chunk_buf) {
                    memcpy(chunk_buf + LWS_PRE, chunk_message, total_len);

                    for (int i = 0; i < g_client_count; i++) {
                        if (g_connected_clients[i]) {
                            lws_write(g_connected_clients[i], chunk_buf + LWS_PRE, total_len, LWS_WRITE_TEXT);
                        }
                    }
                    free(chunk_buf);
                }
                free(chunk_message);
            }
        }
    }
}

/**
 * 广播AEC处理完成（兼容旧版本）
 */
void websocket_broadcast_aec_complete(const char *output_file __attribute__((unused)), int success) {
    websocket_broadcast_aec_complete_with_data(NULL, 0, success);
}

/**
 * 回声消除处理线程
 */
void *aec_processing_thread(void *arg) {
    aec_processing_context_t *ctx = (aec_processing_context_t *)arg;

    SpeexEchoState *echo_state = NULL;
    SpeexPreprocessState *preprocess_state = NULL;

    int16_t mic_buffer[AEC_FRAME_SIZE];
    int16_t ref_buffer[AEC_FRAME_SIZE];
    int16_t output_buffer[AEC_FRAME_SIZE];
    int16_t processed_buffer[AEC_FRAME_SIZE];

    int frames_processed = 0;
    int ret = 0;

    LOG_INFO(MODULE_AUDIO_REALTIME, "🎯 AEC processing thread started");
    LOG_INFO(MODULE_AUDIO_REALTIME, "Mic samples: %d, Ref samples: %d", ctx->mic_samples, ctx->ref_samples);

    if (!ctx->mic_data || !ctx->ref_data || ctx->mic_samples <= 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Invalid input data");
        ret = -1;
        goto cleanup;
    }

    // 分配输出缓冲区
    ctx->output_samples = ctx->mic_samples; // 输出长度与麦克风输入相同
    ctx->output_data = malloc(ctx->output_samples * sizeof(int16_t));
    if (!ctx->output_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate output buffer");
        ret = -1;
        goto cleanup;
    }

    // 初始化回声消除器
    echo_state = speex_echo_state_init(AEC_FRAME_SIZE, AEC_TAIL_LENGTH);
    if (!echo_state) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to initialize echo canceller");
        ret = -1;
        goto cleanup;
    }

    // 设置采样率
    int sample_rate = AEC_SAMPLE_RATE;
    speex_echo_ctl(echo_state, SPEEX_ECHO_SET_SAMPLING_RATE, &sample_rate);

    // 初始化预处理器
    preprocess_state = speex_preprocess_state_init(AEC_FRAME_SIZE, AEC_SAMPLE_RATE);
    if (!preprocess_state) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to initialize preprocessor");
        ret = -1;
        goto cleanup;
    }

    // 关联回声消除器
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_STATE, echo_state);

    // 配置噪声抑制
    int denoise = 1;
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_DENOISE, &denoise);
    int noise_suppress = -15;
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noise_suppress);

    // 配置残余回声抑制
    int echo_suppress = -40;
    int echo_suppress_active = -50;
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_SUPPRESS, &echo_suppress);
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_SUPPRESS_ACTIVE, &echo_suppress_active);

    LOG_INFO(MODULE_AUDIO_REALTIME, "🚀 Starting AEC processing...");

    // 逐帧处理音频数据
    int mic_pos = 0, ref_pos = 0, output_pos = 0;

    while (ctx->is_processing && mic_pos < ctx->mic_samples) {
        // 准备麦克风数据帧
        int mic_frame_samples = (mic_pos + AEC_FRAME_SIZE <= ctx->mic_samples) ?
                               AEC_FRAME_SIZE : (ctx->mic_samples - mic_pos);

        if (mic_frame_samples > 0) {
            memcpy(mic_buffer, &ctx->mic_data[mic_pos], mic_frame_samples * sizeof(int16_t));
            // 如果不足一帧，用零填充
            if (mic_frame_samples < AEC_FRAME_SIZE) {
                memset(&mic_buffer[mic_frame_samples], 0,
                       (AEC_FRAME_SIZE - mic_frame_samples) * sizeof(int16_t));
            }
        }

        // 准备参考信号数据帧
        int ref_frame_samples = (ref_pos + AEC_FRAME_SIZE <= ctx->ref_samples) ?
                               AEC_FRAME_SIZE : (ctx->ref_samples - ref_pos);

        if (ref_frame_samples > 0) {
            memcpy(ref_buffer, &ctx->ref_data[ref_pos], ref_frame_samples * sizeof(int16_t));
            // 如果不足一帧，用零填充
            if (ref_frame_samples < AEC_FRAME_SIZE) {
                memset(&ref_buffer[ref_frame_samples], 0,
                       (AEC_FRAME_SIZE - ref_frame_samples) * sizeof(int16_t));
            }
        } else {
            // 参考信号已结束，用静音填充
            memset(ref_buffer, 0, AEC_FRAME_SIZE * sizeof(int16_t));
        }

        // 参考信号增益调整
        const float ref_gain = 0.5f;
        for (int i = 0; i < AEC_FRAME_SIZE; i++) {
            float scaled = ref_buffer[i] * ref_gain;
            if (scaled > 32767.0f) scaled = 32767.0f;
            if (scaled < -32768.0f) scaled = -32768.0f;
            ref_buffer[i] = (int16_t)scaled;
        }

        // 执行回声消除
        speex_echo_cancellation(echo_state, mic_buffer, ref_buffer, output_buffer);

        // 执行预处理（噪声抑制和残余回声抑制）
        memcpy(processed_buffer, output_buffer, AEC_FRAME_SIZE * sizeof(int16_t));
        speex_preprocess_run(preprocess_state, processed_buffer);

        // 保存处理结果
        int output_frame_samples = (output_pos + mic_frame_samples <= ctx->output_samples) ?
                                  mic_frame_samples : (ctx->output_samples - output_pos);

        if (output_frame_samples > 0) {
            memcpy(&ctx->output_data[output_pos], processed_buffer,
                   output_frame_samples * sizeof(int16_t));
        }

        // 更新位置
        mic_pos += mic_frame_samples;
        ref_pos += ref_frame_samples;
        output_pos += output_frame_samples;

        frames_processed++;
        ctx->progress = frames_processed;

        // 每100帧广播一次进度
        if (frames_processed % 100 == 0) {
            websocket_broadcast_aec_progress(frames_processed, ctx->total_frames);
        }
    }

    float total_seconds = (float)frames_processed * AEC_FRAME_SIZE / AEC_SAMPLE_RATE;
    LOG_INFO(MODULE_AUDIO_REALTIME, "✅ AEC processing complete: %d frames (%.2f seconds)",
             frames_processed, total_seconds);

cleanup:
    // 清理资源
    if (echo_state) speex_echo_state_destroy(echo_state);
    if (preprocess_state) speex_preprocess_state_destroy(preprocess_state);

    // 广播处理完成（包含处理结果数据）
    if (ret == 0 && ctx->output_data && ctx->output_samples > 0) {
        // 将处理结果编码为base64并发送
        char *base64_output = malloc(ctx->output_samples * sizeof(int16_t) * 2); // base64需要更多空间
        if (base64_output) {
            int encoded_len = base64_encode((unsigned char*)ctx->output_data,
                                          ctx->output_samples * sizeof(int16_t),
                                          base64_output,
                                          ctx->output_samples * sizeof(int16_t) * 2);

            if (encoded_len > 0) {
                websocket_broadcast_aec_complete_with_data(base64_output, ctx->output_samples, 1);
            } else {
                websocket_broadcast_aec_complete_with_data(NULL, 0, 0);
            }
            free(base64_output);
        } else {
            websocket_broadcast_aec_complete_with_data(NULL, 0, 0);
        }
    } else {
        websocket_broadcast_aec_complete_with_data(NULL, 0, 0);
    }

    ctx->is_processing = 0;

    LOG_INFO(MODULE_AUDIO_REALTIME, "🎯 AEC processing thread finished");
    return NULL;
}

/**
 * 广播音频状态到所有客户端
 */
void websocket_broadcast_audio_status(const char *status) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"audio_status_notify\",\"status\": \"%s\",\"aec_delay_ms\":%d}",
             status, g_aec_reference_delay_ms);

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket broadcast: Audio status to %d clients", g_client_count);
}

/**
 * 发送PCM音频数据到浏览器
 */
void websocket_send_audio_data(const uint8_t *pcm_data, size_t data_size,
                              int sample_rate, int channels) {
    // 添加调试信息
    static int debug_count = 0;
    debug_count++;
    if (debug_count % 50 == 1) {  // 每50次调用打印一次状态
        LOG_INFO(MODULE_AUDIO_REALTIME, "🎵 Audio send check: context=%p, clients=%d, streaming=%d",
                 (void*)g_ws_context, g_client_count, g_audio_streaming_enabled);
    }

    if (!g_ws_context || g_client_count == 0 || !g_audio_streaming_enabled) {
        if (debug_count % 50 == 1) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ Audio send blocked: context=%s, clients=%s, streaming=%s",
                     g_ws_context ? "OK" : "NULL",
                     g_client_count > 0 ? "OK" : "NONE",
                     g_audio_streaming_enabled ? "ON" : "OFF");
        }
        return;
    }

    // 验证音频数据参数
    if (!pcm_data || data_size == 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "❌ Invalid audio data: data=%p, size=%zu", pcm_data, data_size);
        return;
    }

    // 确保数据大小是采样点的整数倍
    int bytes_per_sample = sizeof(int16_t) * channels;
    if (data_size % bytes_per_sample != 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "❌ Audio data size %zu not aligned to sample size %d",
                  data_size, bytes_per_sample);
        return;
    }

    // 计算Base64编码后的大小
    size_t encoded_size = ((data_size + 2) / 3) * 4;

    // 如果数据太大，分块发送而不是跳过
    const size_t max_chunk_size = MAX_WS_MESSAGE_SIZE / 3; // 留出JSON格式的空间
    if (data_size > max_chunk_size) {
        // 分块发送
        size_t offset = 0;
        int chunk_index = 0;
        while (offset < data_size) {
            size_t chunk_size = (data_size - offset > max_chunk_size) ? max_chunk_size : (data_size - offset);

            // 确保块大小对齐到采样点边界
            chunk_size = (chunk_size / bytes_per_sample) * bytes_per_sample;
            if (chunk_size == 0) break;

            websocket_send_audio_chunk(pcm_data + offset, chunk_size, sample_rate, channels, chunk_index);
            offset += chunk_size;
            chunk_index++;
        }
        return;
    }

    char *encoded_data = malloc(encoded_size + 1);
    if (!encoded_data) return;

    // 简单的Base64编码
    size_t encoded_len = 0;
    for (size_t i = 0; i < data_size; i += 3) {
        uint32_t octet_a = i < data_size ? pcm_data[i] : 0;
        uint32_t octet_b = i + 1 < data_size ? pcm_data[i + 1] : 0;
        uint32_t octet_c = i + 2 < data_size ? pcm_data[i + 2] : 0;

        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

        encoded_data[encoded_len++] = base64_chars[(triple >> 3 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 2 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 1 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 0 * 6) & 0x3F];
    }

    // 处理填充
    if (data_size % 3 == 1) {
        encoded_data[encoded_len - 1] = '=';
        encoded_data[encoded_len - 2] = '=';
    } else if (data_size % 3 == 2) {
        encoded_data[encoded_len - 1] = '=';
    }

    encoded_data[encoded_len] = '\0';

    // 创建JSON消息
    char message[MAX_WS_MESSAGE_SIZE];
    int message_len = snprintf(message, sizeof(message),
                              "{\"type\":\"audio_data\",\"sample_rate\":%d,\"channels\":%d,\"data\":\"%s\"}",
                              sample_rate, channels, encoded_data);

    free(encoded_data);

    if (message_len >= MAX_WS_MESSAGE_SIZE) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Final message too large: %d bytes, max: %d",
                 message_len, MAX_WS_MESSAGE_SIZE);
        return; // 消息太大
    }

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    memcpy(p, message, message_len);

    // 发送到所有连接的客户端
    int sent_count = 0;
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            int result = lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
            if (result >= 0) {
                sent_count++;
            }
        }
    }

    // 每100次发送打印一次统计
    static int send_count = 0;
    send_count++;
    if (send_count % 100 == 1) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "📤 Audio data sent: %d bytes to %d clients (total sends: %d)",
                 message_len, sent_count, send_count);
    }
}

/**
 * 广播音频流控制消息
 */
void websocket_broadcast_audio_stream_control(const char *action) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"audio_stream_notify\",\"action\":\"%s\",\"clients\":%d}",
             action, g_audio_stream_clients);

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }
}
