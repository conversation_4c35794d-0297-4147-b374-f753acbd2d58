#include "websocket_server.h"
#include "aec_handler.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>
#include <unistd.h>
#include <time.h>
#include <pthread.h>

// 外部变量声明
extern volatile int g_aec_reference_delay_ms;          // AEC参考延时
extern int g_auto_adjust_enabled;             // 自动调整是否启用
extern int g_audio_streaming_enabled;         // 音频流是否启用
extern int g_audio_stream_clients;            // 音频流客户端数量

// WebSocket服务器全局变量
static struct lws_context *g_ws_context = NULL;
static struct lws *g_connected_clients[10];
static int g_client_count = 0;

// 音频流控制变量定义
int g_audio_streaming_enabled = 0;
int g_audio_stream_clients = 0;


/**
 * WebSocket协议回调函数 - 处理AEC控制
 */
int callback_aec_control(struct lws *wsi, enum lws_callback_reasons reason,
                         void *user __attribute__((unused)), void *in, size_t len) {
    static char response[4096]; // 使用较小的静态缓冲区用于响应
    unsigned char *buf = NULL;
    unsigned char *p = NULL;
    int response_len;

    switch (reason) {
        case LWS_CALLBACK_ESTABLISHED:
            // 新客户端连接
            if (g_client_count < 10) {
                g_connected_clients[g_client_count++] = wsi;
                LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 WebSocket client connected (total: %d)", g_client_count);
#if USE_AEC_TEST_ENABLE
                // 清理AEC上下文，为新连接准备
                aec_cleanup_context();
#endif
                // 发送当前AEC延时状态
                snprintf(response, sizeof(response),
                         "{\"type\":\"aec_delay_notify\",\"delay_ms\":%d,\"status\":\"connected\"}",
                         g_aec_reference_delay_ms);
                response_len = strlen(response);

                // 动态分配发送缓冲区
                buf = malloc(LWS_PRE + response_len);
                if (buf) {
                    p = &buf[LWS_PRE];
                    memcpy(p, response, response_len);
                    lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
                    free(buf);
                }
            }
            break;

        case LWS_CALLBACK_CLOSED:
            // 客户端断开连接
            for (int i = 0; i < g_client_count; i++) {
                if (g_connected_clients[i] == wsi) {
                    // 移除断开的客户端
                    for (int j = i; j < g_client_count - 1; j++) {
                        g_connected_clients[j] = g_connected_clients[j + 1];
                    }
                    g_client_count--;
#if USE_AEC_TEST_ENABLE
                    // 清理AEC上下文
                    aec_cleanup_context();
#endif
                    // 如果这个客户端正在接收音频流，减少计数
                    if (g_audio_stream_clients > 0) {
                        g_audio_stream_clients--;
                        LOG_INFO(MODULE_AUDIO_REALTIME, "Audio stream client disconnected (remaining: %d)", g_audio_stream_clients);
                    }

                    LOG_INFO(MODULE_AUDIO_REALTIME, "🔌 WebSocket client disconnected (remaining: %d)", g_client_count);
                    break;
                }
            }
            break;

        case LWS_CALLBACK_RECEIVE:
            // 接收到客户端消息
            if (len > 0) {
                char *message = (char *)in;
                message[len] = '\0'; // 确保字符串结束

                // 解析JSON消息
                json_object *root = json_tokener_parse(message);
                if (root != NULL) {

                    json_object *type_obj;
                    if (json_object_object_get_ex(root, "type", &type_obj)) {
                        const char *msg_type = json_object_get_string(type_obj);

                        // 处理不同类型的消息
                        if (strcmp(msg_type, "set_aec_delay") == 0) {
                            // 设置AEC延时
                            json_object *delay_obj;
                            if (json_object_object_get_ex(root, "delay_ms", &delay_obj)) {
                                int new_delay = json_object_get_int(delay_obj);
                                int old_delay = g_aec_reference_delay_ms;
                                g_aec_reference_delay_ms = new_delay;

                                snprintf(response, sizeof(response),
                                         "{\"type\":\"aec_delay_response\",\"old_delay\":%d,\"new_delay\":%d,\"status\":\"success\"}",
                                         old_delay, new_delay);

                                // 广播延时变化
                                websocket_broadcast_aec_delay_change(old_delay, new_delay);
                            } else {
                                snprintf(response, sizeof(response),
                                         "{\"type\":\"aec_delay_response\",\"status\":\"error\",\"message\":\"Missing delay_ms parameter\"}");
                            }

                        } else if (strcmp(msg_type, "get_aec_delay") == 0) {
                            // 获取当前AEC延时
                            snprintf(response, sizeof(response),
                                     "{\"type\":\"aec_delay_response\",\"delay_ms\":%d,\"status\":\"current\"}",
                                     g_aec_reference_delay_ms);

                        } else if (strcmp(msg_type, "toggle_auto_adjust") == 0) {
                            // 切换自动调整
                            int old_enabled = g_auto_adjust_enabled;
                            g_auto_adjust_enabled = !g_auto_adjust_enabled;

                            snprintf(response, sizeof(response),
                                     "{\"type\":\"auto_adjust_response\",\"old_enabled\":%s,\"new_enabled\":%s,\"status\":\"toggled\"}",
                                     old_enabled ? "true" : "false", g_auto_adjust_enabled ? "true" : "false");

                            // 广播自动调整状态变化
                            websocket_broadcast_auto_adjust_change(old_enabled, g_auto_adjust_enabled);

                        }  else if (strcmp(msg_type, "start_audio_stream") == 0) {
                            // 开始音频流
                            g_audio_streaming_enabled = 1;
                            g_audio_stream_clients++;

                            snprintf(response, sizeof(response),
                                     "{\"type\":\"audio_stream_response\",\"status\":\"started\",\"clients\":%d}",
                                     g_audio_stream_clients);

                        } else if (strcmp(msg_type, "stop_audio_stream") == 0) {
                            // 停止音频流
                            if (g_audio_stream_clients > 0) {
                                g_audio_stream_clients--;
                            }
                            if (g_audio_stream_clients == 0) {
                                g_audio_streaming_enabled = 0;
                            }

                            snprintf(response, sizeof(response),
                                     "{\"type\":\"audio_stream_response\",\"status\":\"stopped\",\"clients\":%d}",
                                     g_audio_stream_clients);

                        }else if (strcmp(msg_type, "start_mic_upload") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_mic_upload(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "upload_mic_chunk") == 0) {
                            // 委托给AEC处理器
                            aec_handle_upload_mic_chunk(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "start_ref_upload") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_ref_upload(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "upload_ref_chunk") == 0) {
                            // 委托给AEC处理器
                            aec_handle_upload_ref_chunk(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "start_aec_processing") == 0) {
                            // 委托给AEC处理器
                            aec_handle_start_processing(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "stop_aec_processing") == 0) {
                            // 委托给AEC处理器
                            aec_handle_stop_processing(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "get_aec_status") == 0) {
                            // 委托给AEC处理器
                            aec_handle_get_status(message, response, sizeof(response));

                        } else if (strcmp(msg_type, "get_audio_status") == 0) {
                            // 获取音频状态
                            aec_processing_context_t *aec_ctx = aec_get_context();
                            snprintf(response, sizeof(response),
                                     "{\"type\":\"audio_status_response\",\"status\":\"running\",\"aec_delay_ms\":%d,\"auto_adjust_enabled\":%s,\"audio_streaming\":%s,\"aec_processing\":%s}",
                                     g_aec_reference_delay_ms, g_auto_adjust_enabled ? "true" : "false",
                                     g_audio_streaming_enabled ? "true" : "false",
                                     aec_ctx->is_processing ? "true" : "false");

                        } else {
                            snprintf(response, sizeof(response),
                                     "{\"type\":\"error\",\"message\":\"Unknown message type: %s\"}", msg_type);
                        }
                    } else {
                        snprintf(response, sizeof(response),
                                 "{\"type\":\"error\",\"message\":\"Missing type field\"}");
                    }

                    json_object_put(root);
                }



                // 发送响应
                response_len = strlen(response);

                // 动态分配发送缓冲区
                buf = malloc(LWS_PRE + response_len);
                if (buf) {
                    p = &buf[LWS_PRE];
                    memcpy(p, response, response_len);
                    lws_write(wsi, p, response_len, LWS_WRITE_TEXT);
                    free(buf);
                } else {
                    LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate send buffer");
                }
            }
            break;

        default:
            break;
    }

    return 0;
}

// WebSocket协议定义
static struct lws_protocols protocols[] = {
        {
                "aec-control-protocol",
                callback_aec_control,
                      0,
                         1024 * 1024, // 1MB接收缓冲区
                            0, NULL, 0
        },
        { NULL, NULL, 0, 0, 0, NULL, 0 } // 必须以空终结
};

/**
 * 初始化WebSocket服务器
 */
int websocket_server_init(WebSocketServer *server, int port) {
    if (!server) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "WebSocket server pointer is NULL");
        return -1;
    }

    // 初始化服务器结构体
    memset(server, 0, sizeof(WebSocketServer));
    server->port = port;
    server->running = 0;
#if USE_AEC_TEST_ENABLE
    // 初始化AEC处理器
    aec_handler_init();
#endif
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    info.port = port;
    info.protocols = protocols;
    info.gid = -1;
    info.uid = -1;
    info.max_http_header_data = MAX_WS_MESSAGE_SIZE;
    info.max_http_header_pool = 16;
    info.options = LWS_SERVER_OPTION_VALIDATE_UTF8;

    g_ws_context = lws_create_context(&info);
    if (!g_ws_context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket context");
        return -1;
    }

    server->context = g_ws_context;
    g_client_count = 0;

    LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server initialized on port %d", server->port);
    return 0;
}

/**
 * WebSocket服务器线程函数
 */
static void *websocket_server_thread(void *arg) {
    WebSocketServer *server = (WebSocketServer *)arg;

    LOG_INFO(MODULE_AUDIO_REALTIME, "🚀 WebSocket server thread started");

    while (server->running) {
        lws_service(server->context, 50); // 50ms超时
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "🛑 WebSocket server thread stopped");
    return NULL;
}

/**
 * 启动WebSocket服务器
 */
int websocket_server_start(WebSocketServer *server) {
    if (!server || !server->context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "WebSocket server not initialized");
        return -1;
    }

    server->running = 1;

    // 创建WebSocket服务线程
    if (pthread_create(&server->thread, NULL, websocket_server_thread, server) != 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create WebSocket server thread");
        server->running = 0;
        return -1;
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 WebSocket server started on port %d", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 WebSocket protocol: aec-control-protocol");
    LOG_INFO(MODULE_AUDIO_REALTIME, "🔗 Connect: ws://localhost:%d", server->port);

    return 0;
}

/**
 * 运行WebSocket服务器事件循环
 */
void websocket_server_run(WebSocketServer *server) {
    if (!server || !server->context) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "WebSocket server not initialized");
        return;
    }

    while (server->running) {
        lws_service(server->context, 50);
    }
}

/**
 * 停止WebSocket服务器
 */
void websocket_server_stop(WebSocketServer *server) {
    if (server && server->running) {
        server->running = 0;
        LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server stopping...");

        // 等待线程结束
        if (server->thread) {
            pthread_join(server->thread, NULL);
            server->thread = 0;
        }
    }
}

/**
 * 清理WebSocket服务器
 */
void websocket_server_cleanup(WebSocketServer *server) {
    if (server && server->context) {
        lws_context_destroy(server->context);
        server->context = NULL;
        g_ws_context = NULL;
        g_client_count = 0;
#if USE_AEC_TEST_ENABLE
        // 清理AEC处理器
        aec_handler_cleanup();
#endif
        LOG_INFO(MODULE_AUDIO_REALTIME, "WebSocket server cleaned up");
    }
}

/**
 * 广播AEC延时变化到所有客户端
 */
void websocket_broadcast_aec_delay_change(int old_delay, int new_delay) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_delay_notify\",\"old_delay\":%d,\"new_delay\":%d,\"timestamp\":%ld}",
             old_delay, new_delay, time(NULL));

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

/**
 * 广播自动调整状态变化到所有客户端
 */
void websocket_broadcast_auto_adjust_change(int old_enabled, int new_enabled) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"auto_adjust_notify\",\"old_enabled\":%s,\"new_enabled\":%s,\"timestamp\":%ld}",
             old_enabled ? "true" : "false", new_enabled ? "true" : "false", time(NULL));

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}
#if USE_AEC_TEST_ENABLE
/**
 * 广播AEC处理进度
 */
void websocket_broadcast_aec_progress(int progress, int total_frames) {
    if (!g_ws_context || g_client_count == 0) return;

    char message[MAX_WS_MESSAGE_SIZE];
    float percentage = total_frames > 0 ? (float)progress * 100.0f / total_frames : 0.0f;
    float seconds = (float)progress * AEC_FRAME_SIZE / AEC_SAMPLE_RATE;

    snprintf(message, sizeof(message),
             "{\"type\":\"aec_progress\",\"progress\":%d,\"total\":%d,\"percentage\":%.1f,\"seconds\":%.2f}",
             progress, total_frames, percentage, seconds);

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

/**
 * 广播AEC处理完成（包含数据）
 */
void websocket_broadcast_aec_complete_with_data(const char *output_data, int samples, int success) {
    if (!g_ws_context || g_client_count == 0) return;

    // 只发送完成通知，不包含实际数据
    char message[MAX_WS_MESSAGE_SIZE];
    snprintf(message, sizeof(message),
             "{\"type\":\"aec_complete\",\"success\":%s,\"samples\":%d,\"message\":\"%s\"}",
             success ? "true" : "false",
             samples,
             success ? "AEC处理完成，结果数据已保存" : "AEC处理失败");

    unsigned char *buf = malloc(LWS_PRE + strlen(message));
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    int message_len = strlen(message);
    memcpy(p, message, message_len);

    // 广播完成通知到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);

    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC complete notification sent to %d clients", g_client_count);
}
#endif
/**
 * 广播通用消息
 */
void websocket_broadcast_message(const char *message) {
    if (!g_ws_context || g_client_count == 0 || !message) return;

    int message_len = strlen(message);
    unsigned char *buf = malloc(LWS_PRE + message_len);
    if (!buf) return;

    unsigned char *p = &buf[LWS_PRE];
    memcpy(p, message, message_len);

    // 广播到所有连接的客户端
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
        }
    }

    free(buf);
}

// 其他广播函数的占位符实现
void websocket_broadcast_delay_estimate(float estimated_delay_ms, float mic_rms, float ref_rms,
                                        float correlation, int current_delay_ms) {
    // 实现延时估算广播
}

void websocket_broadcast_signal_strength(float mic_rms, float ref_rms, const char* status) {
    // 实现信号强度广播
}

void websocket_broadcast_audio_status(const char *status) {
    // 实现音频状态广播
}

/**
 * 发送PCM音频数据到浏览器
 */
void websocket_send_audio_data(const uint8_t *pcm_data, size_t data_size,
                               int sample_rate, int channels) {
    // 添加调试信息
    static int debug_count = 0;
    debug_count++;
    if (debug_count % 50 == 1) {  // 每50次调用打印一次状态
        LOG_INFO(MODULE_AUDIO_REALTIME, "🎵 Audio send check: context=%p, clients=%d, streaming=%d",
                 (void*)g_ws_context, g_client_count, g_audio_streaming_enabled);
    }

    if (!g_ws_context || g_client_count == 0 || !g_audio_streaming_enabled) {
        if (debug_count % 50 == 1) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "❌ Audio send blocked: context=%s, clients=%s, streaming=%s",
                     g_ws_context ? "OK" : "NULL",
                     g_client_count > 0 ? "OK" : "NONE",
                     g_audio_streaming_enabled ? "ON" : "OFF");
        }
        return;
    }

    // 将PCM数据转换为Base64编码（简化传输）
    // 这里使用简单的方法，实际项目中可能需要更高效的编码
    static char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    // 计算Base64编码后的大小
    size_t encoded_size = ((data_size + 2) / 3) * 4;

    // 限制数据大小，避免WebSocket消息过大
    if (encoded_size > MAX_WS_MESSAGE_SIZE / 2) {
        static int skip_count = 0;
        skip_count++;
        if (skip_count % 10 == 1) {
            LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Audio data too large: %zu bytes (encoded: %zu), max: %d (skipped: %d times)",
                     data_size, encoded_size, MAX_WS_MESSAGE_SIZE / 2, skip_count);
        }
        return; // 数据太大，跳过
    }

    char *encoded_data = malloc(encoded_size + 1);
    if (!encoded_data) return;

    // 简单的Base64编码
    size_t encoded_len = 0;
    for (size_t i = 0; i < data_size; i += 3) {
        uint32_t octet_a = i < data_size ? pcm_data[i] : 0;
        uint32_t octet_b = i + 1 < data_size ? pcm_data[i + 1] : 0;
        uint32_t octet_c = i + 2 < data_size ? pcm_data[i + 2] : 0;

        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

        encoded_data[encoded_len++] = base64_chars[(triple >> 3 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 2 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 1 * 6) & 0x3F];
        encoded_data[encoded_len++] = base64_chars[(triple >> 0 * 6) & 0x3F];
    }

    // 处理填充
    if (data_size % 3 == 1) {
        encoded_data[encoded_len - 1] = '=';
        encoded_data[encoded_len - 2] = '=';
    } else if (data_size % 3 == 2) {
        encoded_data[encoded_len - 1] = '=';
    }

    encoded_data[encoded_len] = '\0';

    // 创建JSON消息
    char message[MAX_WS_MESSAGE_SIZE];
    int message_len = snprintf(message, sizeof(message),
                               "{\"type\":\"audio_data\",\"sample_rate\":%d,\"channels\":%d,\"data\":\"%s\"}",
                               sample_rate, channels, encoded_data);

    free(encoded_data);

    if (message_len >= MAX_WS_MESSAGE_SIZE) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "⚠️ Final message too large: %d bytes, max: %d",
                 message_len, MAX_WS_MESSAGE_SIZE);
        return; // 消息太大
    }

    unsigned char buf[LWS_PRE + MAX_WS_MESSAGE_SIZE];
    unsigned char *p = &buf[LWS_PRE];
    memcpy(p, message, message_len);

    // 发送到所有连接的客户端
    int sent_count = 0;
    for (int i = 0; i < g_client_count; i++) {
        if (g_connected_clients[i]) {
            int result = lws_write(g_connected_clients[i], p, message_len, LWS_WRITE_TEXT);
            if (result >= 0) {
                sent_count++;
            }
        }
    }

    // 每100次发送打印一次统计
    static int send_count = 0;
    send_count++;
    if (send_count % 100 == 1) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "📤 Audio data sent: %d bytes to %d clients (total sends: %d)",
                 message_len, sent_count, send_count);
    }
}

