# Makefile for echo cancellation demo
# run make -f Makefile_echo_demo

CC = gcc
CFLAGS = -Wall -Wextra -O2 -std=c11
LIBS = -lspeexdsp -lm

# 目标文件
TARGET = echo_cancel_demo
SOURCE = echo_cancel_demo.c

# 默认目标
all: $(TARGET)

# 编译demo
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)
	@echo "编译完成: $(TARGET)"
	@echo "使用方法: ./$(TARGET)"

# 清理
clean:
	rm -f $(TARGET)
	@echo "清理完成"

# 运行demo
run: $(TARGET)
	./$(TARGET)

# 检查输出文件
check:
	@if [ -f "../audio_pcm_demo/output.pcm" ]; then \
		echo "输出文件已生成: ../audio_pcm_demo/output.pcm"; \
		ls -lh ../audio_pcm_demo/output.pcm; \
	else \
		echo "输出文件不存在"; \
	fi

.PHONY: all clean run check
