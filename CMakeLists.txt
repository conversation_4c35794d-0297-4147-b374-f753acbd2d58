cmake_minimum_required(VERSION 3.22)
project(mic_dev C CXX)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 11)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -fPIC")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")

# 阿里云SDK预编译库设置
set(ALI_ASR_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/ali_asr)
set(ALI_ASR_INCLUDE_DIR ${ALI_ASR_ROOT}/include)
set(ALI_ASR_LIB_DIR ${ALI_ASR_ROOT}/lib)

# 阿里云SDK静态库
set(ALI_ASR_STATIC_LIB ${ALI_ASR_LIB_DIR}/libalibabacloud-idst-speech.a)

# 查找必要的库
find_package(PkgConfig REQUIRED)

# 查找SpeexDSP库
pkg_check_modules(SPEEXDSP REQUIRED speexdsp)

# 检查是否有SpeexDSP重采样器
include(CheckIncludeFile)
set(CMAKE_REQUIRED_INCLUDES "/usr/local/include;${SPEEXDSP_INCLUDE_DIRS}")
check_include_file("speex/speex_resampler.h" HAVE_SPEEX_RESAMPLER_H)
if(HAVE_SPEEX_RESAMPLER_H)
    add_definitions(-DHAVE_SPEEX_RESAMPLER)
    message(STATUS "SpeexDSP resampler found")
else()
    message(WARNING "SpeexDSP resampler not found, using simple implementation")
endif()

# 查找ALSA库
pkg_check_modules(ALSA REQUIRED alsa)

# 查找libcurl库
find_package(PkgConfig REQUIRED)
pkg_check_modules(CURL REQUIRED libcurl)

# 查找libopus库
pkg_check_modules(OPUS REQUIRED opus)

# 查找libogg库
pkg_check_modules(OGG REQUIRED ogg)

# 查找JSON库
pkg_check_modules(JSON REQUIRED json-c)

# 查找libmicrohttpd库
pkg_check_modules(MICROHTTPD REQUIRED libmicrohttpd)

# 查找libwebsockets库
pkg_check_modules(WEBSOCKETS REQUIRED libwebsockets)

# 查找SpeexDSP库
pkg_check_modules(SPEEXDSP REQUIRED speexdsp)

# 添加数学库和线程库
find_library(MATH_LIBRARY m)
find_package(Threads REQUIRED)

# 设置源文件
set(SOURCES
        src/main.c
        test/audio_file.c
        src/audio_realtime.c
        src/beamforming.c
        src/speex_processor.c
        src/voice_dialog.c
        src/voice_events.c
        src/app_resampler.c
        src/audio_covert.c
        src/logger.c
        src/ali_asr.cpp
        src/app_tts.c
        src/http_server.c
        src/websocket_server.c
        src/aec_handler.c
)

# 创建可执行文件
add_executable(mic_dev ${SOURCES})

# 包含头文件目录
target_include_directories(mic_dev PRIVATE
    src
    /usr/local/include
    ${SPEEXDSP_INCLUDE_DIRS}
    ${ALSA_INCLUDE_DIRS}
    ${CURL_INCLUDE_DIRS}
    ${OPUS_INCLUDE_DIRS}
    ${OGG_INCLUDE_DIRS}
    ${JSON_INCLUDE_DIRS}
    ${MICROHTTPD_INCLUDE_DIRS}
    ${WEBSOCKETS_INCLUDE_DIRS}
    # 阿里云SDK头文件路径
    ${ALI_ASR_INCLUDE_DIR}
)

# 添加本地库路径
link_directories(/usr/local/lib)

# 链接库
target_link_libraries(mic_dev
    ${SPEEXDSP_LIBRARIES}
    ${ALSA_LIBRARIES}
    ${CURL_LIBRARIES}
    ${OPUS_LIBRARIES}
    ${OGG_LIBRARIES}
    ${JSON_LIBRARIES}
    ${MICROHTTPD_LIBRARIES}
    ${WEBSOCKETS_LIBRARIES}
    ${MATH_LIBRARY}
    Threads::Threads
    speexdsp  # 确保链接SpeexDSP库
    opus      # 确保链接Opus库
    ogg       # 确保链接Ogg库
    json-c    # 确保链接JSON库
    # 阿里云SDK静态库
    ${ALI_ASR_STATIC_LIB}
    # 阿里云SDK依赖的系统库
    ssl
    crypto
    uuid
    dl
)

# 编译选项
target_compile_options(mic_dev PRIVATE
    ${SPEEXDSP_CFLAGS_OTHER}
    ${ALSA_CFLAGS_OTHER}
    -Wall
    -Wextra
    -O2
)

# 链接选项
target_link_directories(mic_dev PRIVATE
    ${SPEEXDSP_LIBRARY_DIRS}
    ${ALSA_LIBRARY_DIRS}
    ${OPUS_LIBRARY_DIRS}
    ${OGG_LIBRARY_DIRS}
)

# 打印配置信息
message(STATUS "SpeexDSP found: ${SPEEXDSP_FOUND}")
message(STATUS "SpeexDSP include dirs: ${SPEEXDSP_INCLUDE_DIRS}")
message(STATUS "SpeexDSP libraries: ${SPEEXDSP_LIBRARIES}")
message(STATUS "ALSA found: ${ALSA_FOUND}")
message(STATUS "ALSA include dirs: ${ALSA_INCLUDE_DIRS}")
message(STATUS "ALSA libraries: ${ALSA_LIBRARIES}")
message(STATUS "Opus found: ${OPUS_FOUND}")
message(STATUS "Opus include dirs: ${OPUS_INCLUDE_DIRS}")
message(STATUS "Opus libraries: ${OPUS_LIBRARIES}")
message(STATUS "Ogg found: ${OGG_FOUND}")
message(STATUS "Ogg include dirs: ${OGG_INCLUDE_DIRS}")
message(STATUS "Ogg libraries: ${OGG_LIBRARIES}")
message(STATUS "JSON found: ${JSON_FOUND}")
message(STATUS "JSON include dirs: ${JSON_INCLUDE_DIRS}")
message(STATUS "JSON libraries: ${JSON_LIBRARIES}")
message(STATUS "Math library: ${MATH_LIBRARY}")

# 安装规则
install(TARGETS mic_dev DESTINATION bin)
