#include "http_server.h"
#include "aec_handler.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>
#include "audio_realtime.h"

// 外部变量声明
extern volatile int g_aec_reference_delay_ms;  // AEC参考延时
extern int g_auto_adjust_enabled;  // 自动调整是否启用

// 文件上传结构
typedef struct {
    FILE *fp;
    char filename[256];
    char type[16];
    size_t size;
    struct MHD_PostProcessor *pp;
} upload_context_t;

/**
 * 处理 GET /status 请求
 */
static int handle_get_status(struct MHD_Connection *connection) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    snprintf(response_str, sizeof(response_str),
             "{\"aec_reference_delay_ms\": %d, \"auto_adjust_enabled\": %s, \"status\": \"running\"}",
             g_aec_reference_delay_ms, g_auto_adjust_enabled ? "true" : "false");

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);

    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP GET /status - AEC delay: %d ms", g_aec_reference_delay_ms);
    return ret;
}

/**
 * 处理 POST /set_aec_delay 请求
 */
static int handle_post_set_aec_delay(struct MHD_Connection *connection,
                                     const char *upload_data,
                                     size_t upload_data_size) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    // 解析 JSON 数据
    char *data = strndup(upload_data, upload_data_size);
    json_object *root = json_tokener_parse(data);

    if (root != NULL) {
        json_object *delay_obj;
        if (json_object_object_get_ex(root, "delay_ms", &delay_obj)) {
            int new_delay = json_object_get_int(delay_obj);

            // 验证延时范围 (1-200ms)
            if (new_delay >= 1 && new_delay <= 200) {
                int old_delay = g_aec_reference_delay_ms;
                g_aec_reference_delay_ms = new_delay;

                LOG_INFO(MODULE_AUDIO_REALTIME,
                        "HTTP POST /set_aec_delay - Changed from %d ms to %d ms",
                        old_delay, new_delay);

                snprintf(response_str, sizeof(response_str),
                        "{\"status\": \"success\", \"old_delay_ms\": %d, \"new_delay_ms\": %d}",
                        old_delay, new_delay);
            } else {
                LOG_INFO(MODULE_AUDIO_REALTIME,
                        "HTTP POST /set_aec_delay - Invalid delay: %d ms (range: 1-200)",
                        new_delay);

                snprintf(response_str, sizeof(response_str),
                        "{\"status\": \"error\", \"message\": \"Invalid delay range (1-200ms)\", \"current_delay_ms\": %d}",
                        g_aec_reference_delay_ms);
            }
        } else {
            snprintf(response_str, sizeof(response_str),
                    "{\"status\": \"error\", \"message\": \"Missing delay_ms parameter\"}");
        }
        json_object_put(root);
    } else {
        snprintf(response_str, sizeof(response_str),
                "{\"status\": \"error\", \"message\": \"Invalid JSON format\"}");
    }

    free(data);

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 POST /set_auto_adjust 请求
 */
static int handle_post_set_auto_adjust(struct MHD_Connection *connection,
                                      const char *upload_data, size_t upload_data_size) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    // 复制数据并添加null终止符
    char *data = malloc(upload_data_size + 1);
    if (!data) {
        snprintf(response_str, sizeof(response_str),
                "{\"status\": \"error\", \"message\": \"Memory allocation failed\"}");
        goto create_response;
    }
    memcpy(data, upload_data, upload_data_size);
    data[upload_data_size] = '\0';

    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP POST /set_auto_adjust - Received: %s", data);

    // 解析JSON
    json_object *root = json_tokener_parse(data);
    if (root) {
        json_object *enabled_obj;
        if (json_object_object_get_ex(root, "enabled", &enabled_obj)) {
            int new_enabled = json_object_get_boolean(enabled_obj);
            int old_enabled = g_auto_adjust_enabled;
            g_auto_adjust_enabled = new_enabled;

            LOG_INFO(MODULE_AUDIO_REALTIME,
                    "HTTP POST /set_auto_adjust - Changed from %s to %s",
                    old_enabled ? "enabled" : "disabled",
                    new_enabled ? "enabled" : "disabled");

            snprintf(response_str, sizeof(response_str),
                    "{\"status\": \"success\", \"old_enabled\": %s, \"new_enabled\": %s}",
                    old_enabled ? "true" : "false",
                    new_enabled ? "true" : "false");
        } else {
            snprintf(response_str, sizeof(response_str),
                    "{\"status\": \"error\", \"message\": \"Missing enabled parameter\"}");
        }
        json_object_put(root);
    } else {
        snprintf(response_str, sizeof(response_str),
                "{\"status\": \"error\", \"message\": \"Invalid JSON format\"}");
    }

    free(data);

create_response:
    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 POST /test_auto_adjust 请求 - 手动触发自动调整测试
 */
static int handle_post_test_auto_adjust(struct MHD_Connection *connection,
                                       const char *upload_data, size_t upload_data_size) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP POST /test_auto_adjust - Manual trigger");

    // 模拟5次延时估计，值为10ms左右
    for (int i = 0; i < 5; i++) {
        float test_delay = 8.0f + i * 0.5f; // 8.0, 8.5, 9.0, 9.5, 10.0
        add_delay_estimate(test_delay);
        LOG_INFO(MODULE_AUDIO_REALTIME, "🧪 Test estimate #%d: %.1f ms", i+1, test_delay);
    }

    snprintf(response_str, sizeof(response_str),
            "{\"status\": \"success\", \"message\": \"Auto-adjust test triggered with 5 estimates (8-10ms)\", \"current_delay_ms\": %d}",
            g_aec_reference_delay_ms);

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理文件下载请求
 */
static int handle_file_download(struct MHD_Connection *connection, const char *url) {
    struct MHD_Response *response;
    int ret;

    // 提取文件路径（去掉 /download/ 前缀）
    const char *file_path = url + strlen("/download/");

    // 安全检查：只允许下载outputs目录下的文件
    if (strstr(file_path, "..") != NULL || strncmp(file_path, "outputs/", 8) != 0) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP GET - Invalid file path: %s", file_path);

        const char *error_msg = "{\"status\": \"error\", \"message\": \"Invalid file path\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg), (void*)error_msg, MHD_RESPMEM_PERSISTENT);
        MHD_add_response_header(response, "Content-Type", "application/json");
        ret = MHD_queue_response(connection, MHD_HTTP_FORBIDDEN, response);
        MHD_destroy_response(response);
        return ret;
    }

    // 打开文件
    FILE *file = fopen(file_path, "rb");
    if (!file) {
        LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP GET - File not found: %s", file_path);

        const char *error_msg = "{\"status\": \"error\", \"message\": \"File not found\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg), (void*)error_msg, MHD_RESPMEM_PERSISTENT);
        MHD_add_response_header(response, "Content-Type", "application/json");
        ret = MHD_queue_response(connection, MHD_HTTP_NOT_FOUND, response);
        MHD_destroy_response(response);
        return ret;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 读取文件内容
    char *file_content = malloc(file_size);
    if (!file_content) {
        fclose(file);
        const char *error_msg = "{\"status\": \"error\", \"message\": \"Memory allocation failed\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg), (void*)error_msg, MHD_RESPMEM_PERSISTENT);
        ret = MHD_queue_response(connection, MHD_HTTP_INTERNAL_SERVER_ERROR, response);
        MHD_destroy_response(response);
        return ret;
    }

    size_t read_size = fread(file_content, 1, file_size, file);
    fclose(file);

    if (read_size != file_size) {
        free(file_content);
        const char *error_msg = "{\"status\": \"error\", \"message\": \"File read error\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg), (void*)error_msg, MHD_RESPMEM_PERSISTENT);
        ret = MHD_queue_response(connection, MHD_HTTP_INTERNAL_SERVER_ERROR, response);
        MHD_destroy_response(response);
        return ret;
    }

    // 创建响应
    response = MHD_create_response_from_buffer(file_size, file_content, MHD_RESPMEM_MUST_FREE);
    if (!response) {
        free(file_content);
        return MHD_NO;
    }

    // 设置响应头
    MHD_add_response_header(response, "Content-Type", "audio/wav");
    MHD_add_response_header(response, "Content-Disposition", "attachment; filename=\"aec_output.wav\"");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");

    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);

    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP GET - File download: %s (%ld bytes)", file_path, file_size);
    return ret;
}

/**
 * 文件上传迭代器
 */
static int upload_iterator(void *cls, enum MHD_ValueKind kind, const char *key,
                          const char *filename, const char *content_type,
                          const char *transfer_encoding, const char *data,
                          uint64_t off, size_t size) {
    upload_context_t *ctx = (upload_context_t *)cls;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Upload iterator: key=%s, filename=%s, size=%zu, off=%lu",
             key ? key : "NULL", filename ? filename : "NULL", size, (unsigned long)off);

    if (strcmp(key, "type") == 0) {
        // 获取文件类型 - 优先处理type字段
        size_t copy_len = size < sizeof(ctx->type) - 1 ? size : sizeof(ctx->type) - 1;
        strncpy(ctx->type, data, copy_len);
        ctx->type[copy_len] = '\0';
        LOG_INFO(MODULE_AUDIO_REALTIME, "File type: %s", ctx->type);
    } else if (strcmp(key, "file") == 0 && filename) {
        // 处理文件数据
        if (ctx->fp == NULL) {
            // 创建临时文件，使用时间戳确保唯一性
            snprintf(ctx->filename, sizeof(ctx->filename), "/tmp/aec_%s_%ld.pcm",
                    ctx->type[0] ? ctx->type : "temp", (long)time(NULL));
            ctx->fp = fopen(ctx->filename, "wb");
            if (!ctx->fp) {
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create temp file: %s", ctx->filename);
                return MHD_NO;
            }
            LOG_INFO(MODULE_AUDIO_REALTIME, "Created temp file: %s for %s (type: %s)",
                    ctx->filename, filename, ctx->type[0] ? ctx->type : "unknown");
        }

        if (size > 0) {
            size_t written = fwrite(data, 1, size, ctx->fp);
            ctx->size += written;
            if (written != size) {
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to write file data: %zu/%zu", written, size);
                return MHD_NO;
            }
            if (ctx->size % 10240 == 0) { // 每10KB记录一次
                LOG_INFO(MODULE_AUDIO_REALTIME, "Wrote %zu bytes, total: %zu", written, ctx->size);
            }
        }
    }

    return MHD_YES;
}

/**
 * 处理 POST /upload_audio 请求
 */
static int handle_post_upload_audio(struct MHD_Connection *connection, void **con_cls,
                                   const char *upload_data, size_t *upload_data_size) {
    upload_context_t *ctx = (upload_context_t *)*con_cls;

    if (ctx == NULL) {
        // 第一次调用，初始化上下文和POST处理器
        ctx = calloc(1, sizeof(upload_context_t));
        if (!ctx) return MHD_NO;

        ctx->pp = MHD_create_post_processor(connection, 65536, upload_iterator, ctx);
        if (!ctx->pp) {
            LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create POST processor");
            free(ctx);
            return MHD_NO;
        }

        *con_cls = ctx;
        LOG_INFO(MODULE_AUDIO_REALTIME, "Initialized upload context");
        return MHD_YES;
    }

    if (*upload_data_size > 0) {
        // 处理上传数据
        LOG_INFO(MODULE_AUDIO_REALTIME, "Processing upload data: %zu bytes", *upload_data_size);
        if (MHD_post_process(ctx->pp, upload_data, *upload_data_size) != MHD_YES) {
            LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to process upload data");
        }
        *upload_data_size = 0;
        return MHD_YES;
    }

    // 上传完成，处理文件
    struct MHD_Response *response;
    char response_str[512];
    int ret;

    // 销毁POST处理器
    if (ctx->pp) {
        MHD_destroy_post_processor(ctx->pp);
        ctx->pp = NULL;
    }

    if (ctx->fp) {
        fclose(ctx->fp);
        ctx->fp = NULL;

        LOG_INFO(MODULE_AUDIO_REALTIME, "File upload completed: %s (%zu bytes), type: %s", ctx->filename, ctx->size, ctx->type);

        // 检查文件是否真的存在
        FILE *check_file = fopen(ctx->filename, "rb");
        if (check_file) {
            fseek(check_file, 0, SEEK_END);
            long actual_size = ftell(check_file);
            fclose(check_file);
            LOG_INFO(MODULE_AUDIO_REALTIME, "File verification: %s exists with %ld bytes", ctx->filename, actual_size);
        } else {
            LOG_ERROR(MODULE_AUDIO_REALTIME, "File verification failed: %s does not exist", ctx->filename);
        }

        // 将文件数据加载到AEC处理器
        if (strcmp(ctx->type, "mic") == 0) {
#if USE_AEC_TEST_ENABLE
            LOG_INFO(MODULE_AUDIO_REALTIME, "Loading microphone file: %s", ctx->filename);
            if (aec_load_mic_file(ctx->filename) == 0) {
                snprintf(response_str, sizeof(response_str),
                        "{\"status\":\"success\",\"type\":\"mic\",\"size\":%zu,\"message\":\"Microphone file uploaded\"}",
                        ctx->size);
                LOG_INFO(MODULE_AUDIO_REALTIME, "Microphone file uploaded: %zu bytes", ctx->size);
            } else {
                snprintf(response_str, sizeof(response_str),
                        "{\"status\":\"error\",\"message\":\"Failed to load microphone file\"}");
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to load microphone file: %s", ctx->filename);
            }
#endif
        } else if (strcmp(ctx->type, "ref") == 0) {
#if USE_AEC_TEST_ENABLE
            if (aec_load_ref_file(ctx->filename) == 0) {
                snprintf(response_str, sizeof(response_str),
                        "{\"status\":\"success\",\"type\":\"ref\",\"size\":%zu,\"message\":\"Reference file uploaded\"}",
                        ctx->size);
                LOG_INFO(MODULE_AUDIO_REALTIME, "Reference file uploaded: %zu bytes", ctx->size);
            } else {
                snprintf(response_str, sizeof(response_str),
                        "{\"status\":\"error\",\"message\":\"Failed to load reference file\"}");
            }
#endif
        } else {
            snprintf(response_str, sizeof(response_str),
                    "{\"status\":\"error\",\"message\":\"Unknown file type\"}");
        }

        // 删除临时文件
        unlink(ctx->filename);
    } else {
        snprintf(response_str, sizeof(response_str),
                "{\"status\":\"error\",\"message\":\"No file received\"}");
    }

    response = MHD_create_response_from_buffer(strlen(response_str), response_str, MHD_RESPMEM_MUST_COPY);
    if (!response) {
        free(ctx);
        return MHD_NO;
    }

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);

    free(ctx);
    return ret;
}

/**
 * 处理 GET /aec_output.wav 请求
 */
static int handle_get_aec_output(struct MHD_Connection *connection) {
    unsigned char *wav_data = NULL;
    int wav_size = 0;
    int ret = -1;
#if USE_AEC_TEST_ENABLE
    // 获取AEC输出数据
    if (aec_get_output_wav(&wav_data, &wav_size) != 0) {
        // 没有数据，返回404
        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen("No AEC output available"), "No AEC output available", MHD_RESPMEM_PERSISTENT);
        if (!response) return MHD_NO;

        MHD_add_response_header(response, "Content-Type", "text/plain");
        MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
        int ret = MHD_queue_response(connection, MHD_HTTP_NOT_FOUND, response);
        MHD_destroy_response(response);
        return ret;
    }

    // 创建响应
    struct MHD_Response *response = MHD_create_response_from_buffer(
        wav_size, wav_data, MHD_RESPMEM_MUST_FREE);
    if (!response) {
        free(wav_data);
        return MHD_NO;
    }

    // 设置响应头
    MHD_add_response_header(response, "Content-Type", "audio/wav");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    MHD_add_response_header(response, "Content-Disposition", "attachment; filename=\"aec_output.wav\"");

    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);

    LOG_INFO(MODULE_AUDIO_REALTIME, "Served AEC output WAV file: %d bytes", wav_size);
#endif
    return ret;
}

/**
 * 处理 OPTIONS 请求 (CORS预检)
 */
static int handle_options_request(struct MHD_Connection *connection) {
    struct MHD_Response *response;
    int ret;

    response = MHD_create_response_from_buffer(0, "", MHD_RESPMEM_PERSISTENT);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    MHD_add_response_header(response, "Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    MHD_add_response_header(response, "Access-Control-Allow-Headers", "Content-Type");
    ret = MHD_queue_response(connection, MHD_HTTP_OK, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 404 错误
 */
static int handle_not_found(struct MHD_Connection *connection) {
    struct MHD_Response *response;
    char response_str[MAX_RESPONSE_SIZE];
    int ret;

    snprintf(response_str, sizeof(response_str),
             "{\"status\": \"error\", \"message\": \"Not found\", \"available_endpoints\": [\"/status\", \"/set_aec_delay\", \"/set_auto_adjust\", \"/test_auto_adjust\"]}");

    response = MHD_create_response_from_buffer(strlen(response_str),
                                              response_str,
                                              MHD_RESPMEM_MUST_COPY);
    if (!response) return MHD_NO;

    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    ret = MHD_queue_response(connection, MHD_HTTP_NOT_FOUND, response);
    MHD_destroy_response(response);
    return ret;
}

/**
 * 处理 HTTP 请求的回调函数 - 主路由分发器
 */
static int request_handler(void *cls, struct MHD_Connection *connection,
                          const char *url, const char *method,
                          const char *version, const char *upload_data,
                          size_t *upload_data_size, void **con_cls) {

    // 处理 GET /status 请求
    if (strcmp(method, "GET") == 0 && strcmp(url, "/status") == 0) {
        return handle_get_status(connection);
    }

    // 处理文件下载请求 GET /download/*
    if (strcmp(method, "GET") == 0 && strncmp(url, "/download/", 10) == 0) {
        return handle_file_download(connection, url);
    }

    // 处理 POST /set_aec_delay 请求
    if (strcmp(method, "POST") == 0 && strcmp(url, "/set_aec_delay") == 0) {
        if (*con_cls == NULL) {
            // 初始化 POST 数据处理
            *con_cls = malloc(1); // 简单标记
            return MHD_YES; // 继续接收数据
        }

        if (*upload_data_size > 0) {
            int ret = handle_post_set_aec_delay(connection, upload_data, *upload_data_size);
            *upload_data_size = 0; // 数据已处理
            return ret;
        }
        return MHD_YES;
    }

    // 处理 POST /set_auto_adjust 请求
    if (strcmp(method, "POST") == 0 && strcmp(url, "/set_auto_adjust") == 0) {
        if (*con_cls == NULL) {
            // 初始化 POST 数据处理
            *con_cls = malloc(1); // 简单标记
            return MHD_YES; // 继续接收数据
        }

        if (*upload_data_size > 0) {
            int ret = handle_post_set_auto_adjust(connection, upload_data, *upload_data_size);
            *upload_data_size = 0; // 数据已处理
            return ret;
        }
        return MHD_YES;
    }

    // 处理 POST /test_auto_adjust 请求
    if (strcmp(method, "POST") == 0 && strcmp(url, "/test_auto_adjust") == 0) {
        if (*con_cls == NULL) {
            // 初始化 POST 数据处理
            *con_cls = malloc(1); // 简单标记
            return MHD_YES; // 继续接收数据
        }

        if (*upload_data_size > 0) {
            int ret = handle_post_test_auto_adjust(connection, upload_data, *upload_data_size);
            *upload_data_size = 0; // 数据已处理
            return ret;
        }
        return MHD_YES;
    }

    // 处理 POST /upload_audio 请求
    if (strcmp(method, "POST") == 0 && strcmp(url, "/upload_audio") == 0) {
        return handle_post_upload_audio(connection, con_cls, upload_data, upload_data_size);
    }

    // 处理 GET /aec_output.wav 请求
    if (strcmp(method, "GET") == 0 && strcmp(url, "/aec_output.wav") == 0) {
        return handle_get_aec_output(connection);
    }

    // 处理 OPTIONS 请求 (CORS预检)
    if (strcmp(method, "OPTIONS") == 0) {
        return handle_options_request(connection);
    }

    // 默认响应：404
    return handle_not_found(connection);
}

/**
 * 初始化HTTP服务器
 */
int http_server_init(HttpServer *server, int port) {
    if (!server) return -1;
    
    memset(server, 0, sizeof(HttpServer));
    server->port = port;
    server->running = 0;
    
    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP server initialized on port %d", port);
    return 0;
}

/**
 * 启动HTTP服务器
 */
int http_server_start(HttpServer *server) {
    if (!server || server->running) return -1;
    
    server->daemon = MHD_start_daemon(MHD_USE_SELECT_INTERNALLY, 
                                     server->port, 
                                     NULL, NULL,
                                     &request_handler, 
                                     NULL,
                                     MHD_OPTION_END);
    
    if (server->daemon == NULL) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to start HTTP server on port %d", server->port);
        return -1;
    }
    
    server->running = 1;
    LOG_INFO(MODULE_AUDIO_REALTIME, "🌐 HTTP server started on port %d", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "📡 API endpoints:");
    LOG_INFO(MODULE_AUDIO_REALTIME, "   GET  http://localhost:%d/status", server->port);
    LOG_INFO(MODULE_AUDIO_REALTIME, "   POST http://localhost:%d/set_aec_delay", server->port);
    
    return 0;
}

/**
 * 停止HTTP服务器
 */
void http_server_stop(HttpServer *server) {
    if (!server || !server->running) return;
    
    if (server->daemon) {
        MHD_stop_daemon(server->daemon);
        server->daemon = NULL;
    }
    
    server->running = 0;
    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP server stopped");
}

/**
 * 清理HTTP服务器
 */
void http_server_cleanup(HttpServer *server) {
    if (!server) return;
    
    http_server_stop(server);
    LOG_INFO(MODULE_AUDIO_REALTIME, "HTTP server cleaned up");
}
