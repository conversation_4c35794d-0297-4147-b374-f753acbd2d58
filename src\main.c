#include "audio_processor.h"
#include "audio_realtime.h"
#include "logger.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <signal.h>
#include <time.h>
#include <unistd.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include "ali_asr.h"
#include "app_tts.h"
#include "app_config.h"

// 全局变量用于信号处理
static volatile int g_running = 1;

// 全局变量存储最新的识别结果
static char g_latest_recognition_text[2048] = {0};
static bool g_has_new_result = false;
static pthread_mutex_t g_result_mutex = PTHREAD_MUTEX_INITIALIZER;
/**
 * 阿里云ASR结果回调函数
 */
void ali_asr_result_callback(const AliASRResult *result, void *user_data) {
    (void) user_data; // 未使用的参数

    if (!result) {
        return;
    }

    printf("\n🎯 === Ali ASR Callback ===\n");

    if (result->success && strlen(result->text) > 0) {
        printf("📝 Text: %s\n", result->text);
        printf("🔄 Type: %s\n", result->is_final ? "Final" : "Intermediate");
        printf("📝 Text: %s\n", result->text);
        // 调用tts 播报 call_tts_service
        if (result->is_final) {
            printf("🆔 Sentence ID: %d\n", result->sentence_id);
            printf("📊 Confidence: %.2f\n", result->confidence);
            printf("⏱️ Time: %d - %d ms\n", result->begin_time, result->end_time);

            // 调用TTS服务进行播报
            call_tts_service(result->text);

            // 存储最终识别结果，供语音处理流程使用
            pthread_mutex_lock(&g_result_mutex);
            strncpy(g_latest_recognition_text, result->text, sizeof(g_latest_recognition_text) - 1);
            g_latest_recognition_text[sizeof(g_latest_recognition_text) - 1] = '\0';
            g_has_new_result = true;
            pthread_mutex_unlock(&g_result_mutex);

            printf("💾 Recognition result stored for TTS processing\n");
        }
    } else if (!result->success) {
        printf("❌ Error: %s\n", result->error_msg);
    }

    printf("===========================\n\n");
}

/**
 * 信号处理函数
 */
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping processing...\n", sig);
    g_running = 0;
}

/**
 * 初始化音频处理器
 */
int audio_processor_init(AudioProcessor *processor) {
    if (!processor) {
        return -1;
    }

    memset(processor, 0, sizeof(AudioProcessor));

    // 初始化波束形成
    if (beamforming_init(processor, MIC_SPACING, TARGET_ANGLE) != 0) {
        fprintf(stderr, "Failed to initialize beamforming\n");
        return -1;
    }

    // 初始化SpeexDSP
    if (speex_processor_init(processor) != 0) {
        fprintf(stderr, "Failed to initialize SpeexDSP\n");
        return -1;
    }

    processor->initialized = true;
    processor->frames_processed = 0;

    printf("Audio processor initialized successfully\n");
    return 0;
}

/**
 * 清理音频处理器
 */
void audio_processor_cleanup(AudioProcessor *processor) {
    if (!processor) {
        return;
    }

    speex_processor_cleanup(processor);
    processor->initialized = false;

    printf("Audio processor cleaned up\n");
}

/**
 * 打印处理统计信息
 */
void print_processing_stats(const AudioProcessor *processor) {
    if (!processor) {
        return;
    }

    printf("\n=== Processing Statistics ===\n");
    printf("Frames processed: %lu\n", processor->frames_processed);
    printf("Duration processed: %.2f seconds\n",
           (double) processor->frames_processed * FRAME_SIZE / SAMPLE_RATE);

    print_beamforming_stats(processor);
    print_speex_stats(processor);
    printf("============================\n");
}

/**
 * 显示使用帮助
 */
void print_usage(const char *program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("\n");
    printf("Real-time microphone processing with Ali ASR integration\n");
    printf("\n");
    printf("Options:\n");
    printf("  --realtime              - Real-time processing mode (default)\n");
    printf("  --capture-device DEVICE - Specify capture device (default: auto-detect USB mic)\n");
    printf("  --playback-device DEVICE- Specify playback device (default: default)\n");
    printf("  --bt-delay MS           - Set Bluetooth delay compensation in ms (50-250, default: 250)\n");
    printf("  --log-level LEVEL       - Set global log level (TRACE,DEBUG,INFO,WARN,ERROR,FATAL)\n");
    printf("  --module-log MODULE:LEVEL - Set specific module log level\n");
    printf("  --list-devices          - List available audio devices\n");
    printf("  --help                  - Show this help\n");
    printf("\n");
    printf("Log Modules:\n");
    printf("  MAIN, AUDIO_RT, VOICE_DLG, VOICE_EVT, SPEECH_UP, ALI_ASR,\n");
    printf("  RESAMPLER, AUDIO_PROC, BEAMFORM, SPEEX_PROC, CHANNEL\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s --realtime\n", program_name);
    printf("  %s --realtime --capture-device hw:1,0\n", program_name);
    printf("  %s --realtime --bt-delay 200\n", program_name);
    printf("  %s --realtime --log-level DEBUG\n", program_name);
    printf("  %s --realtime --module-log VOICE_EVT:TRACE\n", program_name);
    printf("  %s --list-devices\n", program_name);
}

/**
 * 实时音频处理函数
 */
int run_realtime_mode(const char *capture_device, const char *playback_device) {
    printf("=== Real-time Microphone Array Audio Processor ===\n");

    // 初始化音频处理器
    AudioProcessor processor;
    if (audio_processor_init(&processor) != 0) {
        fprintf(stderr, "Failed to initialize audio processor\n");
        return 1;
    }

    // 初始化实时音频处理器
    RealtimeAudioProcessor rt_processor;
    if (realtime_audio_init(&rt_processor, capture_device, playback_device) != 0) {
        fprintf(stderr, "Failed to initialize realtime audio processor\n");
        audio_processor_cleanup(&processor);
        return 1;
    }

    // 关联音频处理器
    rt_processor.audio_processor = &processor;

    // 启动实时处理
    if (realtime_audio_start(&rt_processor) != 0) {
        fprintf(stderr, "Failed to start realtime audio processing\n");
        realtime_audio_cleanup(&rt_processor);
        audio_processor_cleanup(&processor);
        return 1;
    }

    printf("Real-time processing started. Press Ctrl+C to stop.\n");
    printf("Processing 2-channel microphone input -> enhanced single-channel output\n");

    // 主循环 - 等待用户中断
    while (g_running) {
        sleep(1);

        //// 每10秒打印一次统计信息
        //static int stats_counter = 0;
        //if (++stats_counter >= 10) {
        //    print_realtime_stats(&rt_processor);
        //    print_processing_stats(&processor);
        //    stats_counter = 0;
        //}
    }

    // 清理
    realtime_audio_stop(&rt_processor);
    realtime_audio_cleanup(&rt_processor);
    audio_processor_cleanup(&processor);

    printf("Real-time processing stopped.\n");
    return 0;
}


/**
 * 主函数
 */
/**
 * 解析日志级别字符串
 */
LogLevel parse_log_level(const char *level_str) {
    if (strcasecmp(level_str, "TRACE") == 0) return LOG_LEVEL_TRACE;
    if (strcasecmp(level_str, "DEBUG") == 0) return LOG_LEVEL_DEBUG;
    if (strcasecmp(level_str, "INFO") == 0) return LOG_LEVEL_INFO;
    if (strcasecmp(level_str, "WARN") == 0) return LOG_LEVEL_WARN;
    if (strcasecmp(level_str, "ERROR") == 0) return LOG_LEVEL_ERROR;
    if (strcasecmp(level_str, "FATAL") == 0) return LOG_LEVEL_FATAL;
    if (strcasecmp(level_str, "OFF") == 0) return LOG_LEVEL_OFF;
    return LOG_LEVEL_INFO; // 默认
}

/**
 * 解析模块名称
 */
ModuleId parse_module_name(const char *module_str) {
    if (strcasecmp(module_str, "MAIN") == 0) return MODULE_MAIN;
    if (strcasecmp(module_str, "AUDIO_RT") == 0) return MODULE_AUDIO_REALTIME;
    if (strcasecmp(module_str, "VOICE_DLG") == 0) return MODULE_VOICE_DIALOG;
    if (strcasecmp(module_str, "VOICE_EVT") == 0) return MODULE_VOICE_EVENTS;
    if (strcasecmp(module_str, "SPEECH_UP") == 0) return MODULE_SPEECH_UPLOAD;
    if (strcasecmp(module_str, "ALI_ASR") == 0) return MODULE_ALI_ASR;
    if (strcasecmp(module_str, "RESAMPLER") == 0) return MODULE_APP_RESAMPLER;
    if (strcasecmp(module_str, "AUDIO_PROC") == 0) return MODULE_AUDIO_PROCESSOR;
    if (strcasecmp(module_str, "BEAMFORM") == 0) return MODULE_BEAMFORMING;
    if (strcasecmp(module_str, "SPEEX_PROC") == 0) return MODULE_SPEEX_PROCESSOR;
    if (strcasecmp(module_str, "CHANNEL") == 0) return MODULE_CHANNEL_PROCESSING;
    return MODULE_COUNT; // 无效
}

/**
 * 配置各模块的日志级别
 */
void configure_log_levels(void) {
    // 只显示VOICE_EVENTS模块的日志，其他模块全部关闭
    logger_set_module_level(MODULE_MAIN, LOG_LEVEL_OFF);
    logger_set_module_level(MODULE_AUDIO_REALTIME, LOG_LEVEL_INFO);  // 启用VAD调试
    logger_set_module_level(MODULE_VOICE_DIALOG, LOG_LEVEL_INFO);
    logger_set_module_level(MODULE_VOICE_EVENTS, LOG_LEVEL_INFO);  // 只显示这个模块
    logger_set_module_level(MODULE_SPEECH_UPLOAD, LOG_LEVEL_OFF);
    logger_set_module_level(MODULE_ALI_ASR, LOG_LEVEL_OFF);
    logger_set_module_level(MODULE_APP_RESAMPLER, LOG_LEVEL_OFF);
    logger_set_module_level(MODULE_AUDIO_PROCESSOR, LOG_LEVEL_INFO);
    logger_set_module_level(MODULE_BEAMFORMING, LOG_LEVEL_OFF);
    logger_set_module_level(MODULE_SPEEX_PROCESSOR, LOG_LEVEL_INFO);
    logger_set_module_level(MODULE_CHANNEL_PROCESSING, LOG_LEVEL_OFF);

    printf("📋 Log configuration: Only VOICE_EVENTS module enabled (DEBUG level)\n");
    printf("   All other modules: OFF\n");
    printf("   VOICE_EVENTS: %s\n", logger_level_name(logger_get_module_level(MODULE_VOICE_EVENTS)));
}

int main(int argc, char *argv[]) {
    // 初始化日志系统
    if (logger_init(LOG_LEVEL_INFO) != 0) {
        fprintf(stderr, "Failed to initialize logger\n");
        return 1;
    }

    // 配置各模块的日志级别
    configure_log_levels();

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 默认参数
    const char *capture_device = NULL;
    const char *playback_device = NULL;

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
            print_usage(argv[0]);
            return 0;
        } else if (strcmp(argv[i], "--list-devices") == 0) {
            list_audio_devices();
            return 0;
        } else if (strcmp(argv[i], "--realtime") == 0) {

        } else if (strcmp(argv[i], "--capture-device") == 0) {
            if (i + 1 < argc) {
                capture_device = argv[++i];
            } else {
                fprintf(stderr, "Error: --capture-device requires a device name\n");
                return 1;
            }
        } else if (strcmp(argv[i], "--playback-device") == 0) {
            if (i + 1 < argc) {
                playback_device = argv[++i];
            } else {
                fprintf(stderr, "Error: --playback-device requires a device name\n");
                return 1;
            }
        } else if (strcmp(argv[i], "--log-level") == 0) {
            if (i + 1 < argc) {
                LogLevel level = parse_log_level(argv[++i]);
                logger_set_global_level(level);
                printf("Global log level set to %s\n", logger_level_name(level));
            } else {
                fprintf(stderr, "Error: --log-level requires a level (TRACE,DEBUG,INFO,WARN,ERROR,FATAL)\n");
                return 1;
            }
        } else if (strcmp(argv[i], "--module-log") == 0) {
            if (i + 1 < argc) {
                char *module_log = argv[++i];
                char *colon = strchr(module_log, ':');
                if (colon) {
                    *colon = '\0';
                    ModuleId module = parse_module_name(module_log);
                    LogLevel level = parse_log_level(colon + 1);
                    if (module < MODULE_COUNT) {
                        logger_set_module_level(module, level);
                        printf("Module %s log level set to %s\n",
                               logger_module_name(module), logger_level_name(level));
                    } else {
                        fprintf(stderr, "Error: Unknown module '%s'\n", module_log);
                        return 1;
                    }
                } else {
                    fprintf(stderr, "Error: --module-log format should be MODULE:LEVEL\n");
                    return 1;
                }
            } else {
                fprintf(stderr, "Error: --module-log requires MODULE:LEVEL format\n");
                return 1;
            }
        } else {
            fprintf(stderr, "Error: Unknown argument '%s'\n", argv[i]);
            print_usage(argv[0]);
            return 1;
        }
    }

    // 运行实时模式
    printf("🚀 Starting realtime mode with on-demand Ali ASR...\n");

    // 初始化阿里云ASR（但不连接）
    printf("🔧 Initializing Ali ASR module...\n");
    if (ali_asr_init(16000, 1, ali_asr_result_callback, NULL) != 0) {
        printf("❌ Failed to initialize Ali ASR\n");
        return 1;
    }

    printf("✅ Ali ASR module ready (will connect on voice detection)\n");

    static char auto_device[256];  // 使用static确保字符串在函数返回后仍然有效
    if (!capture_device) {
        // 自动检测USB麦克风
        if (find_usb_mic_device(auto_device, sizeof(auto_device)) == 0) {
            capture_device = auto_device;
        } else {
            capture_device = "default";
        }
    }

    int result = run_realtime_mode(capture_device, playback_device);

    // 清理日志系统
    logger_cleanup();

    return result;
}
