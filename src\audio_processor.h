#ifndef AUDIO_PROCESSOR_H
#define AUDIO_PROCESSOR_H

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <speex/speex_echo.h>
#include <speex/speex_preprocess.h>
#include <speex/speex_resampler.h>

// 前向声明
typedef struct RealtimeAudioProcessor RealtimeAudioProcessor;

// 音频参数常量
#define SAMPLE_RATE         16000    // 采样率 16kHz
#define CHANNELS            2        // 2声道麦克风阵列（修正为实际硬件配置）
#define BITS_PER_SAMPLE     16       // 16位采样
#define FRAME_SIZE          160      // 每帧样本数 (10ms @ 16kHz)
#define BYTES_PER_SAMPLE    2        // 16位 = 2字节
#define BUFFER_SIZE         (FRAME_SIZE * CHANNELS)

// 波束形成参数
#define MIC_SPACING         0.05     // 麦克风间距 5cm
#define SOUND_SPEED         343.0    // 声速 m/s
#define TARGET_ANGLE        0.0      // 目标角度（正前方）
#define MAX_DELAY_SAMPLES   8        // 最大延迟样本数
//#define BEAMFORMING_GAIN    8.0      // 波束形成增益系数（增加到8倍）
#define BEAMFORMING_GAIN    4.0  // 减少回音

// SpeexDSP参数
#define FILTER_LENGTH       480      // AEC滤波器长度
#define TAIL_LENGTH         (4800*2)     // 回声尾长度（200ms @ 48kHz，增加以处理更长的回声）

// 增益控制参数
#define OUTPUT_GAIN         3.0      // 最终输出增益
#define AGC_TARGET_LEVEL    20000    // AGC目标电平（适中）
#define NOISE_SUPPRESS_DB   -20      // 噪声抑制强度（适中）

// 音频处理状态结构
typedef struct {
    // 输入缓冲区
    int16_t input_buffer[BUFFER_SIZE];
    int16_t channel_buffers[CHANNELS][FRAME_SIZE];
    
    // 波束形成
    float beamformed_buffer[FRAME_SIZE];
    int delay_samples[CHANNELS];
    float channel_weights[CHANNELS];
    
    // SpeexDSP处理器
    SpeexEchoState *echo_state[2]; // 立体声回声消除变量（左右声道）
    SpeexPreprocessState *preprocess_state; //噪声抑制变量
    SpeexPreprocessState *vad_state; // 人声检查
    SpeexResamplerState *resampler_state;  // 重采样器 (48k -> 16k)

    // VAD结果
    int last_vad_result;  // 最后一次VAD检测结果 (1=语音, 0=静音)

    // 输出缓冲区
    int16_t output_buffer[FRAME_SIZE];

    // 处理统计
    uint64_t frames_processed;
    bool initialized;
} AudioProcessor;

// 文件处理结构
typedef struct {
    FILE *input_file;
    FILE *output_file;
    FILE *ref_file;  // 参考信号文件（可选）
    
    uint64_t total_samples;
    uint64_t processed_samples;
    bool has_reference;
} AudioFileHandler;

// 函数声明

// 初始化和清理
int audio_processor_init(AudioProcessor *processor);
void audio_processor_cleanup(AudioProcessor *processor);

// 文件操作
int audio_file_open(AudioFileHandler *handler, const char *input_path,
                   const char *output_path, const char *ref_path);
void audio_file_close(AudioFileHandler *handler);
int audio_file_read_frame(AudioFileHandler *handler, int16_t *buffer, int samples);
int audio_file_read_reference(AudioFileHandler *handler, int16_t *buffer, int samples);
int audio_file_write_frame(AudioFileHandler *handler, const int16_t *buffer, int samples);
double audio_file_get_progress(const AudioFileHandler *handler);

// 波束形成
int beamforming_init(AudioProcessor *processor, float mic_spacing, float target_angle);
void beamforming_process(AudioProcessor *processor, const int16_t channels[][FRAME_SIZE],
                        float *output, int samples);
// 48kHz波束形成：处理16位交错输入数据，输出16位单声道
void beamforming_process_48khz(AudioProcessor *processor, const int16_t *interleaved_input,
                              int16_t *output, int frame_size);

// SpeexDSP处理
int speex_processor_init(AudioProcessor *processor);
void speex_processor_cleanup(AudioProcessor *processor);
int audio_echo_cancel(AudioProcessor *processor, const int16_t *input,
                     const int16_t *reference, int16_t *output);
int audio_noise_suppress(AudioProcessor *processor, int16_t *audio, int samples);

// AGC控制函数
int set_agc_parameters(AudioProcessor *processor, float target_level,
                      float max_gain, float max_attenuation);
int set_agc_gain_multiplier(AudioProcessor *processor, float gain_multiplier);

// 工具函数
void print_processing_stats(const AudioProcessor *processor);
void print_beamforming_stats(const AudioProcessor *processor);
void print_speex_stats(const AudioProcessor *processor);

#endif // AUDIO_PROCESSOR_H
