# AEC文件处理功能说明

## 🎯 功能概述

这个功能将 `echo_cancel_demo.c` 的回声消除能力集成到了WebSocket应用中，提供了完整的文件上传、处理和下载流程。

## 🔧 主要特性

### 1. 文件上传
- 支持PCM格式音频文件上传
- 同时上传麦克风文件和参考信号文件
- 文件大小显示和验证
- Base64编码传输

### 2. 实时处理
- 使用SpeexDSP库进行回声消除
- 实时进度显示
- 可中断处理过程
- 多线程处理，不阻塞WebSocket通信

### 3. 音频试听
- 上传文件的即时试听
- 处理结果的在线播放
- 自动WAV格式转换
- 音频控件支持

### 4. 文件下载
- 处理完成后可下载结果
- HTTP文件服务
- 安全路径验证

## 📁 文件结构

```
uploads/          # 上传文件存储目录
├── mic_*.pcm    # 麦克风文件
└── ref_*.pcm    # 参考信号文件

outputs/          # 处理结果存储目录
└── aec_output_*.pcm  # 回声消除后的文件
```

## 🎮 使用流程

### 1. 准备音频文件
- 格式：PCM (Raw Audio)
- 采样率：48kHz
- 声道：单声道
- 位深：16bit

### 2. 上传文件
1. 在WebSocket测试页面中找到"AEC文件处理"部分
2. 选择麦克风PCM文件
3. 选择参考信号PCM文件
4. 点击"上传文件"按钮

### 3. 开始处理
1. 上传成功后，"开始处理"按钮变为可用
2. 点击开始处理
3. 观察实时进度条
4. 可随时点击"停止处理"中断

### 4. 试听和下载
1. 处理完成后可试听原始文件和处理结果
2. 点击"下载结果"保存处理后的音频

## 🔌 WebSocket消息协议

### 上传文件
```json
{
  "type": "upload_aec_files",
  "mic_data": "base64_encoded_pcm_data",
  "ref_data": "base64_encoded_pcm_data", 
  "mic_name": "mic_file.pcm",
  "ref_name": "ref_file.pcm"
}
```

### 开始处理
```json
{
  "type": "start_aec_processing"
}
```

### 停止处理
```json
{
  "type": "stop_aec_processing"
}
```

### 获取状态
```json
{
  "type": "get_aec_status"
}
```

## 📊 服务器响应

### 上传响应
```json
{
  "type": "upload_response",
  "status": "success|error",
  "message": "描述信息"
}
```

### 处理响应
```json
{
  "type": "aec_response", 
  "status": "started|stopped|error",
  "total_frames": 12345
}
```

### 进度通知
```json
{
  "type": "aec_progress",
  "progress": 1234,
  "total": 12345,
  "percentage": 10.0,
  "seconds": 25.6
}
```

### 完成通知
```json
{
  "type": "aec_complete",
  "success": true,
  "output_file": "outputs/aec_output_1234567890.pcm"
}
```

## 🔧 技术实现

### 后端 (C)
- **WebSocket服务器**: 处理文件上传和控制消息
- **AEC处理线程**: 独立线程执行回声消除
- **HTTP文件服务**: 提供文件下载功能
- **SpeexDSP集成**: 使用专业音频处理库

### 前端 (JavaScript)
- **文件读取**: FileReader API读取本地文件
- **Base64编码**: 二进制数据编码传输
- **WAV生成**: 动态生成WAV头部用于试听
- **进度显示**: 实时更新处理进度

## ⚙️ 配置参数

```c
#define AEC_SAMPLE_RATE 48000    // 采样率
#define AEC_FRAME_SIZE 480       // 帧大小 (10ms)
#define AEC_TAIL_LENGTH 14400    // 尾长 (300ms)
```

## 🚀 性能优化

1. **流式处理**: 逐帧处理，内存占用低
2. **多线程**: 处理不阻塞WebSocket通信
3. **进度反馈**: 每100帧更新一次进度
4. **资源管理**: 自动清理临时文件

## 🔒 安全考虑

1. **路径验证**: 严格限制文件访问路径
2. **文件大小**: 可配置最大文件大小限制
3. **类型检查**: 验证文件格式
4. **临时清理**: 定期清理临时文件

## 🐛 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件格式是否为PCM
   - 确认文件大小不超过限制
   - 检查网络连接

2. **处理失败**
   - 确认文件采样率为48kHz
   - 检查文件完整性
   - 查看服务器日志

3. **无法试听**
   - 检查浏览器音频支持
   - 确认文件格式转换正确
   - 尝试刷新页面

### 日志查看
```bash
# 查看实时日志
tail -f /var/log/mic_dev.log

# 搜索AEC相关日志
grep "AEC" /var/log/mic_dev.log
```

## 📈 扩展功能

### 可能的改进
1. **多格式支持**: 支持WAV、MP3等格式
2. **批量处理**: 同时处理多个文件
3. **参数调节**: 可调整AEC参数
4. **质量评估**: 自动评估处理效果
5. **云存储**: 集成云存储服务

### API扩展
1. **RESTful API**: 提供HTTP API接口
2. **文件管理**: 文件列表、删除等操作
3. **用户系统**: 多用户文件隔离
4. **历史记录**: 处理历史和统计

这个功能为音频处理提供了完整的工作流程，从文件上传到结果下载，所有操作都可以通过Web界面完成。
