#!/usr/bin/env python3
"""
双路音频采集数据分析工具
分析麦克风和参考信号的时间同步性、延时等
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import argparse
import os

def load_pcm_file(filename, sample_rate=48000, channels=1, dtype=np.int16):
    """加载PCM文件"""
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return None, None

    # 读取PCM数据
    data = np.fromfile(filename, dtype=dtype)

    if channels > 1:
        # 重塑为多声道格式
        if len(data) % channels != 0:
            # 截断到最近的完整帧
            data = data[:len(data) - (len(data) % channels)]
        data = data.reshape(-1, channels)

    # 转换为浮点数并归一化
    data = data.astype(np.float32) / 32768.0

    # 生成时间轴
    if channels > 1:
        duration = len(data) / sample_rate
        time_axis = np.linspace(0, duration, len(data))
    else:
        duration = len(data) / sample_rate
        time_axis = np.linspace(0, duration, len(data))

    print(f"📁 {filename}: {len(data)} 样本, {duration:.2f}秒, {channels}声道")

    return data, time_axis

def calculate_rms(data, window_size=1600):
    """计算RMS值"""
    if len(data.shape) > 1:
        # 多声道，取平均
        data = np.mean(data, axis=1)
    
    # 计算滑动窗口RMS
    rms_values = []
    for i in range(0, len(data) - window_size, window_size // 4):
        window = data[i:i + window_size]
        rms = np.sqrt(np.mean(window ** 2))
        rms_values.append(rms)
    
    return np.array(rms_values)

def estimate_delay_correlation(mic_data, ref_data, sample_rate=48000):
    """使用互相关估计延时"""
    # 确保数据为单声道
    if len(mic_data.shape) > 1:
        mic_mono = np.mean(mic_data, axis=1)
    else:
        mic_mono = mic_data

    if len(ref_data.shape) > 1:
        ref_mono = np.mean(ref_data, axis=1)
    else:
        ref_mono = ref_data

    # 取较短的长度
    min_len = min(len(mic_mono), len(ref_mono))
    mic_mono = mic_mono[:min_len]
    ref_mono = ref_mono[:min_len]

    # 只分析前10秒的数据以提高速度
    analysis_samples = min(min_len, sample_rate * 10)
    mic_segment = mic_mono[:analysis_samples]
    ref_segment = ref_mono[:analysis_samples]

    # 计算RMS，检查信号强度
    mic_rms = np.sqrt(np.mean(mic_segment**2))
    ref_rms = np.sqrt(np.mean(ref_segment**2))

    print(f"🔍 信号强度: 麦克风RMS={mic_rms:.4f}, 参考RMS={ref_rms:.4f}")

    if mic_rms < 0.001 or ref_rms < 0.001:
        print("⚠️  警告: 信号太弱，延时估计可能不准确")

    # 计算互相关
    correlation = signal.correlate(mic_segment, ref_segment, mode='full')

    # 找到最大相关性的位置
    max_corr_idx = np.argmax(np.abs(correlation))
    delay_samples = max_corr_idx - (len(ref_segment) - 1)

    # 转换为毫秒
    delay_ms = delay_samples * 1000.0 / sample_rate

    # 归一化相关系数
    max_correlation = correlation[max_corr_idx] / (np.linalg.norm(mic_segment) * np.linalg.norm(ref_segment))

    return delay_samples, delay_ms, max_correlation, mic_rms, ref_rms

# 绘图功能已简化，如需详细图表分析请安装matplotlib和scipy

def main():
    parser = argparse.ArgumentParser(description='双路音频采集数据分析')
    parser.add_argument('--mic', default='mic_capture_50s.pcm', help='麦克风PCM文件')
    parser.add_argument('--ref', default='ref_capture_50s.pcm', help='参考信号PCM文件')

    args = parser.parse_args()

    print("🎯 双路音频采集数据分析")
    print("=" * 40)

    # 加载数据（48kHz单声道）
    mic_data, mic_time = load_pcm_file(args.mic, 48000, 1)
    ref_data, ref_time = load_pcm_file(args.ref, 48000, 1)

    if mic_data is None or ref_data is None:
        print("❌ 无法加载音频文件")
        return

    # 延时分析
    print("\n🔍 分析延时...")
    delay_samples, delay_ms, correlation, mic_rms, ref_rms = estimate_delay_correlation(mic_data, ref_data)

    print(f"\n📊 延时分析结果:")
    print(f"  • 估计延时: {delay_ms:.1f} ms")
    print(f"  • 延时样本数: {delay_samples}")
    print(f"  • 相关系数: {correlation:.3f}")

    if abs(delay_ms) > 100:
        print("⚠️  警告: 延时超过100ms，可能存在同步问题")
    elif abs(delay_ms) < 5:
        print("✅ 延时很小，同步性良好")
    else:
        print("ℹ️  延时在合理范围内")

    # 信号质量分析
    print(f"\n📈 信号质量分析:")
    print(f"  • 麦克风 RMS: {mic_rms:.4f}")
    print(f"  • 参考信号 RMS: {ref_rms:.4f}")

    if ref_rms > 0:
        print(f"  • 信号比 (ref/mic): {ref_rms/mic_rms:.2f}")

    if mic_rms < 0.001:
        print("⚠️  警告: 麦克风信号很弱")
    if ref_rms < 0.001:
        print("⚠️  警告: 参考信号很弱")

    # 文件大小分析
    mic_size = os.path.getsize(args.mic) if os.path.exists(args.mic) else 0
    ref_size = os.path.getsize(args.ref) if os.path.exists(args.ref) else 0

    print(f"\n📁 文件信息:")
    print(f"  • 麦克风文件: {mic_size} 字节 ({mic_size/1024/1024:.1f} MB)")
    print(f"  • 参考文件: {ref_size} 字节 ({ref_size/1024/1024:.1f} MB)")
    print(f"  • 大小差异: {abs(mic_size-ref_size)} 字节")

    if abs(mic_size - ref_size) > 1024:
        print("⚠️  警告: 文件大小差异较大，可能存在同步问题")

    # 建议
    print(f"\n💡 分析建议:")
    if abs(delay_ms) > 50:
        print(f"  • 当前延时 {delay_ms:.1f}ms 较大，建议调整AEC延时设置")
        suggested_delay = int(abs(delay_ms) + 5)  # 加5ms安全余量
        print(f"  • 建议AEC延时设置: {suggested_delay}ms")

    if correlation < 0.3:
        print(f"  • 相关性 {correlation:.3f} 较低，检查信号源是否正确")

    if mic_rms < 0.01:
        print(f"  • 麦克风信号较弱，检查增益设置")

    if ref_rms < 0.01:
        print(f"  • 参考信号较弱，检查回采设置")

    print(f"\n✅ 分析完成!")

if __name__ == "__main__":
    main()
