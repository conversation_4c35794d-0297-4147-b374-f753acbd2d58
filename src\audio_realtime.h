#ifndef AUDIO_REALTIME_H
#define AUDIO_REALTIME_H

#include "audio_processor.h"
#include "app_resampler.h"
#include <alsa/asoundlib.h>
#include <pthread.h>
#include <stdint.h>
#include <stdbool.h>

#define DIALOG_PROCESS_ENABLE 1

#define SAMPLE_RATE_16K      16000    // 16kHz采样率
#define SAMPLE_RATE_48K      48000    // 48kHz采样率

// 实时音频参数
#define RT_SAMPLE_RATE      SAMPLE_RATE_16K    // 16kHz采样率（SpeexDSP处理）
#define RT_CHANNELS_IN      2        // 输入2声道（修正为实际硬件配置）
#define RT_CHANNELS_OUT     1        // 输出单声道
#define RT_FRAME_SIZE       160      // 每帧样本数（16kHz下10ms，SpeexDSP兼容）

// ALSA音频参数（实际录音格式）
#define ALSA_SAMPLE_RATE    SAMPLE_RATE_48K    // 48kHz采样率
#define ALSA_CHANNELS       2        // 2声道麦克风（实际硬件）
#define ALSA_FRAME_SIZE     480      // 48kHz下10ms帧大小
#define RT_BUFFER_FRAMES    32       // 缓冲区帧数 (增加到32帧)
#define RT_PERIOD_SIZE      320      // ALSA周期大小 (增加到320样本)
#define RT_BUFFER_SIZE      4096     // ALSA缓冲区大小



#define ENABLE_AEC_PLAYBACK_CAPTURE 1  // 启用从播放设备捕获用于回声消除的参考音频
#define PLAY_ENABLE           0        // 开关
#define PLAY_SAMPLE_RATE      SAMPLE_RATE_48K    // 48k
#define PLAY_FRAME_SIZE       480      // 16kHz下10ms帧大小
#define PLAY_CHANNELS         2        //   2声道


// 最优范围：30-50ms更符合实际的物理和系统延迟
// Orange Pi特性：嵌入式设备的延迟通常比PC更小
// 回声消除参考音频延迟（毫秒）
// 我的估计：最优延迟应该在35-45ms之间。
// 注意：现在使用动态变量 g_aec_reference_delay_ms，可通过HTTP API调整
extern volatile int g_aec_reference_delay_ms;  // 动态AEC延时（默认55ms）

// AEC延时自动调整相关
extern int g_auto_adjust_enabled;              // 是否启用自动调整

// 循环缓冲区大小 (增加到更大的缓冲区)
#define RING_BUFFER_SIZE    (RT_FRAME_SIZE * RT_BUFFER_FRAMES * 4)


// 循环缓冲区结构
typedef struct {
    int16_t *buffer;
    int size;
    int write_pos;
    int read_pos;
    int available;
    pthread_mutex_t mutex;
    pthread_cond_t cond_not_empty;
    pthread_cond_t cond_not_full;
} RingBuffer;

// 实时音频处理器结构
struct RealtimeAudioProcessor {
    // ALSA设备句柄
    snd_pcm_t *capture_handle;          // 麦克风物理设备
    snd_pcm_t *playback_handle;         // 扬声器物理设备
#if ENABLE_AEC_PLAYBACK_CAPTURE
    snd_pcm_t *aec_capture_handle;      // AEC参考信号捕获设备 (硬件音频输出监听)
    snd_pcm_t *aec_playback_handle;     // AEC参考信号播放设备 (未使用，保留兼容性)
#endif
    
    // 设备名称
    char *capture_device;
    char *playback_device;
    
    // 缓冲区
    RingBuffer *input_ring;
    RingBuffer *output_ring;
#if ENABLE_AEC_PLAYBACK_CAPTURE
    RingBuffer *playback_capture_ring; // 新增：用于AEC参考的环形缓冲区
#endif
    
    // 线程
    pthread_t capture_thread;
    pthread_t playback_thread;
#if ENABLE_AEC_PLAYBACK_CAPTURE
    pthread_t playback_capture_thread; // 硬件音频输出监听线程：捕获系统所有音频输出作为AEC参考
#endif
    pthread_t process_thread;
    
    // 控制标志
    volatile bool running;
    volatile bool initialized;
    
    // 统计信息
    uint64_t frames_captured;
    uint64_t frames_played;
    uint64_t frames_processed;
    uint64_t underruns;
    uint64_t overruns;
    
    // 音频处理器指针
    void *audio_processor;
};

// 函数声明

// 初始化和清理
int realtime_audio_init(RealtimeAudioProcessor *rt_processor, 
                       const char *capture_device, const char *playback_device);
void realtime_audio_cleanup(RealtimeAudioProcessor *rt_processor);

// 设备管理
int list_audio_devices(void);
int find_usb_mic_device(char *device_name, size_t name_size);

void add_delay_estimate(float estimated_delay_ms) ;

// 缓冲区管理
RingBuffer* ring_buffer_create(int size);
void ring_buffer_destroy(RingBuffer *rb);
int ring_buffer_write(RingBuffer *rb, const int16_t *data, int samples);
int ring_buffer_read(RingBuffer *rb, int16_t *data, int samples);
int ring_buffer_available_read(RingBuffer *rb);
int ring_buffer_advance_read_pos(RingBuffer *rb, int samples);

// ALSA设备配置
int configure_alsa_capture(snd_pcm_t *handle);
int configure_alsa_playback(snd_pcm_t *handle);

// 线程函数
void* capture_thread_func(void *arg);
void* playback_thread_func(void *arg);
void* process_thread_func(void *arg);

// 控制函数
int realtime_audio_start(RealtimeAudioProcessor *rt_processor);
int realtime_audio_stop(RealtimeAudioProcessor *rt_processor);

// 统计和调试
void print_realtime_stats(const RealtimeAudioProcessor *rt_processor);
void print_alsa_error(const char *function, int err);


// 重采样器访问函数
AppResamplerWrapper* get_global_resampler(void);

#endif // AUDIO_REALTIME_H
