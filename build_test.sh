#!/bin/bash

echo "🔨 编译双路音频同步采集测试程序"

# 编译测试程序
gcc -o test_dual_capture test_dual_capture.c -lasound -lpthread -lrt -lm

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo ""
    echo "🚀 使用方法:"
    echo "  ./test_dual_capture"
    echo ""
    echo "📝 说明:"
    echo "  - 程序使用单线程同步采集麦克风和参考信号"
    echo "  - 采样率: 48kHz, 2声道采集 → 1声道保存（左声道）"
    echo "  - 采集时长: 50秒"
    echo "  - 按 Ctrl+C 可提前停止"
    echo "  - 生成两个同步的PCM文件（单声道）"
    echo ""
    echo "📁 输出文件:"
    echo "  - mic_capture_50s.pcm (麦克风左声道数据)"
    echo "  - ref_capture_50s.pcm (参考信号左声道数据)"
    echo ""
    echo "🔧 播放测试命令:"
    echo "  # 播放麦克风录音"
    echo "  aplay -f S16_LE -c 1 -r 48000 mic_capture_50s.pcm"
    echo ""
    echo "  # 播放参考信号录音"
    echo "  aplay -f S16_LE -c 1 -r 48000 ref_capture_50s.pcm"
    echo ""
    echo "🎯 分析要点:"
    echo "  1. 检查两个文件的时间同步性"
    echo "  2. 分析延时差异和相关性"
    echo "  3. 检查信号质量和噪声水平"
    echo "  4. 验证回声消除效果"
    echo ""
    echo "⚠️  注意事项:"
    echo "  - 确保设备 plughw:1,0 (麦克风) 和 plughw:2,0 (参考信号) 存在"
    echo "  - 测试前可用 'arecord -l' 查看可用设备"
    echo "  - 如需修改设备，编辑 test_dual_capture.c 中的设备定义"
else
    echo "❌ 编译失败！"
    echo "请检查是否安装了必要的依赖:"
    echo "  sudo apt install libasound2-dev build-essential"
fi
