# 双路音频同步采集测试工具

## 🎯 目的

这个测试工具用于同步采集麦克风和参考信号（扬声器回采），帮助分析回声消除系统的问题：

1. **时间同步性** - 检查两路信号是否同步
2. **延时分析** - 估计麦克风和参考信号之间的延时
3. **信号质量** - 分析信号强度和噪声水平
4. **回声消除效果** - 为优化AEC参数提供数据

## 📁 文件说明

- `test_dual_capture.c` - 主测试程序源码
- `build_test.sh` - 编译脚本
- `analyze_capture.py` - 数据分析脚本
- `README_test.md` - 使用说明

## 🔧 编译和运行

### 1. 编译程序

```bash
# 给脚本执行权限
chmod +x build_test.sh

# 编译
./build_test.sh
```

### 2. 检查音频设备

```bash
# 查看可用的录音设备
arecord -l

# 示例输出：
# card 1: USB [USB Audio], device 0: USB Audio [USB Audio]
# card 2: Loopback [Loopback], device 0: Loopback PCM [Loopback PCM]
```

### 3. 修改设备配置（如需要）

编辑 `test_dual_capture.c` 中的设备定义：

```c
#define MIC_DEVICE "plughw:1,0"      // 麦克风设备
#define REF_DEVICE "plughw:2,0"      // 参考信号设备
#define SAMPLE_RATE 48000            // 采样率 48kHz
#define CHANNELS 2                   // 采集声道数（立体声）
#define SAVE_CHANNELS 1              // 保存声道数（只保存左声道）
```

### 4. 运行测试

```bash
# 运行50秒采集测试
./test_dual_capture

# 按 Ctrl+C 可提前停止
```

## 📊 数据分析

### 1. 基本分析

```bash
# 使用Python分析脚本
python3 analyze_capture.py

# 或指定文件
python3 analyze_capture.py --mic mic_capture_50s.pcm --ref ref_capture_50s.pcm
```

### 2. 手动播放测试

```bash
# 播放麦克风录音（48kHz单声道）
aplay -f S16_LE -c 1 -r 48000 mic_capture_50s.pcm

# 播放参考信号录音（48kHz单声道）
aplay -f S16_LE -c 1 -r 48000 ref_capture_50s.pcm
```

### 3. 转换为WAV格式（可选）

```bash
# 转换麦克风数据（48kHz单声道）
sox -r 48000 -e signed -b 16 -c 1 mic_capture_50s.pcm mic_capture.wav

# 转换参考信号数据（48kHz单声道）
sox -r 48000 -e signed -b 16 -c 1 ref_capture_50s.pcm ref_capture.wav
```

## 🔍 分析要点

### 1. 同步性检查

- **文件大小**: 两个文件大小应该相近
- **时长差异**: 应该小于10ms
- **同步状态**: 程序会自动评估

### 2. 延时分析

- **估计延时**: 互相关分析得出的延时值
- **相关系数**: 信号相关性，>0.3为良好
- **建议设置**: 程序会给出AEC延时建议

### 3. 信号质量

- **RMS值**: 信号强度指标
- **信号比**: 参考信号与麦克风信号的比值
- **动态范围**: 信号的动态范围

## ⚠️ 常见问题

### 1. 设备打开失败

```
❌ 无法打开设备 plughw:1,0: No such device
```

**解决方法**:
- 使用 `arecord -l` 查看可用设备
- 修改代码中的设备定义
- 确保设备没有被其他程序占用

### 2. 权限问题

```
❌ 无法打开设备: Permission denied
```

**解决方法**:
- 将用户添加到audio组: `sudo usermod -a -G audio $USER`
- 重新登录或重启

### 3. 信号太弱

```
⚠️ 警告: 麦克风信号很弱
```

**解决方法**:
- 检查麦克风增益设置
- 确保音频源正常工作
- 调整ALSA音量控制

### 4. 同步问题

```
⚠️ 警告: 延时超过100ms，可能存在同步问题
```

**解决方法**:
- 检查设备配置是否正确
- 确保两个设备使用相同的时钟源
- 检查系统负载是否过高

## 📈 结果解读

### 延时分析结果示例

```
📊 延时分析结果:
  • 估计延时: 45.2 ms
  • 延时样本数: 723
  • 相关系数: 0.756
```

**解读**:
- **45.2ms延时**: 参考信号比麦克风信号延迟45.2ms
- **相关系数0.756**: 信号相关性良好
- **建议**: AEC延时设置为50ms左右

### 信号质量示例

```
📈 信号质量分析:
  • 麦克风 RMS: 0.0234
  • 参考信号 RMS: 0.1456
  • 信号比 (ref/mic): 6.22
```

**解读**:
- **RMS值**: 信号强度正常
- **信号比6.22**: 参考信号比麦克风信号强6倍，正常

## 🎯 优化建议

1. **延时设置**: 根据分析结果调整AEC延时参数
2. **信号增益**: 调整麦克风和参考信号的增益平衡
3. **采样同步**: 确保两路信号采样同步
4. **环境噪声**: 在安静环境下测试以获得更准确的结果

## 📞 技术支持

如果遇到问题，请提供：
1. 设备列表 (`arecord -l` 输出)
2. 错误信息
3. 系统信息 (`uname -a`)
4. ALSA版本 (`cat /proc/asound/version`)
