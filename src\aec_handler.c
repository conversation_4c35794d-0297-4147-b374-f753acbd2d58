#include "aec_handler.h"
#include "websocket_server.h"
#include "logger.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <json-c/json.h>
#include <speex/speex_echo.h>
#include <speex/speex_preprocess.h>
#include <pthread.h>
#include <unistd.h>
#include <errno.h>
#include "app_config.h"

#if USE_AEC_TEST_ENABLE

// 前向声明
static void aec_start_chunked_result_send(const char *encoded_data, int data_len, int total_chunks);

// 全局AEC上下文
static aec_processing_context_t g_aec_context = {0};

/**
 * Base64解码函数
 */
static int base64_decode(const char *input, unsigned char *output, int max_output_len) {
    static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    int input_len = strlen(input);
    int output_len = 0;
    int i, j;
    unsigned char char_array_4[4], char_array_3[3];

    for (i = 0; i < input_len && output_len < max_output_len; ) {
        for (j = 0; j < 4 && i < input_len; j++, i++) {
            char *p = strchr(base64_chars, input[i]);
            if (p) {
                char_array_4[j] = p - base64_chars;
            } else if (input[i] == '=') {
                char_array_4[j] = 0;
            } else {
                continue; // 跳过无效字符
            }
        }

        if (j >= 2) {
            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            if (output_len < max_output_len) output[output_len++] = char_array_3[0];

            if (j >= 3) {
                char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
                if (output_len < max_output_len) output[output_len++] = char_array_3[1];

                if (j >= 4) {
                    char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
                    if (output_len < max_output_len) output[output_len++] = char_array_3[2];
                }
            }
        }
    }

    return output_len;
}

/**
 * Base64编码函数
 */
static int base64_encode(const unsigned char *input, int input_len, char *output, int max_output_len) {
    static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    int output_len = 0;
    int i;
    unsigned char char_array_3[3], char_array_4[4];

    for (i = 0; i < input_len && output_len < max_output_len - 4; ) {
        int j;
        for (j = 0; j < 3 && i < input_len; j++, i++) {
            char_array_3[j] = input[i];
        }

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
        char_array_4[3] = char_array_3[2] & 0x3f;

        for (int k = 0; k < 4 && output_len < max_output_len - 1; k++) {
            if (k < j + 1) {
                output[output_len++] = base64_chars[char_array_4[k]];
            } else {
                output[output_len++] = '=';
            }
        }
    }

    output[output_len] = '\0';
    return output_len;
}

/**
 * 初始化AEC处理器
 */
void aec_handler_init(void) {
    memset(&g_aec_context, 0, sizeof(g_aec_context));
    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC handler initialized");
}

/**
 * 清理AEC处理器
 */
void aec_handler_cleanup(void) {
    aec_cleanup_context();
    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC handler cleaned up");
}

/**
 * 清理AEC上下文
 */
void aec_cleanup_context() {
    // 清理麦克风数据
    if (g_aec_context.mic_chunks) {
        for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
            if (g_aec_context.mic_chunks[i]) {
                free(g_aec_context.mic_chunks[i]);
            }
        }
        free(g_aec_context.mic_chunks);
        g_aec_context.mic_chunks = NULL;
    }
    if (g_aec_context.mic_chunk_sizes) {
        free(g_aec_context.mic_chunk_sizes);
        g_aec_context.mic_chunk_sizes = NULL;
    }
    if (g_aec_context.mic_data) {
        free(g_aec_context.mic_data);
        g_aec_context.mic_data = NULL;
    }

    // 清理参考信号数据
    if (g_aec_context.ref_chunks) {
        for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
            if (g_aec_context.ref_chunks[i]) {
                free(g_aec_context.ref_chunks[i]);
            }
        }
        free(g_aec_context.ref_chunks);
        g_aec_context.ref_chunks = NULL;
    }
    if (g_aec_context.ref_chunk_sizes) {
        free(g_aec_context.ref_chunk_sizes);
        g_aec_context.ref_chunk_sizes = NULL;
    }
    if (g_aec_context.ref_data) {
        free(g_aec_context.ref_data);
        g_aec_context.ref_data = NULL;
    }

    // 清理输出数据
    if (g_aec_context.output_data) {
        free(g_aec_context.output_data);
        g_aec_context.output_data = NULL;
    }

    // 重置状态
    g_aec_context.mic_expected_chunks = 0;
    g_aec_context.ref_expected_chunks = 0;
    g_aec_context.mic_received_chunks = 0;
    g_aec_context.ref_received_chunks = 0;
    g_aec_context.mic_samples = 0;
    g_aec_context.ref_samples = 0;
    g_aec_context.mic_samples_expected = 0;
    g_aec_context.ref_samples_expected = 0;
    g_aec_context.mic_upload_complete = 0;
    g_aec_context.ref_upload_complete = 0;
    g_aec_context.is_processing = 0;
}

/**
 * 获取AEC上下文
 */
aec_processing_context_t* aec_get_context(void) {
    return &g_aec_context;
}

/**
 * 获取处理结果数据
 */
int aec_get_output_data(char **output_data, int *samples) {
    if (!g_aec_context.output_data || g_aec_context.output_samples <= 0) {
        return -1;
    }

    // 编码输出数据为base64
    int data_size = g_aec_context.output_samples * sizeof(int16_t);
    char *encoded_data = malloc(data_size * 2);
    if (!encoded_data) {
        return -1;
    }

    int encoded_len = base64_encode((unsigned char*)g_aec_context.output_data,
                                   data_size, encoded_data, data_size * 2);

    if (encoded_len > 0) {
        *output_data = encoded_data;
        *samples = g_aec_context.output_samples;
        return 0;
    } else {
        free(encoded_data);
        return -1;
    }
}

/**
 * 获取WAV格式的输出数据
 */
int aec_get_output_wav(unsigned char **wav_data, int *wav_size) {
    if (!g_aec_context.output_data || g_aec_context.output_samples <= 0) {
        return -1;
    }

    // WAV文件头参数
    const int sample_rate = AEC_SAMPLE_RATE;
    const int channels = 1;
    const int bits_per_sample = 16;
    const int data_size = g_aec_context.output_samples * sizeof(int16_t);
    const int file_size = 44 + data_size;

    // 分配WAV缓冲区
    unsigned char *wav_buffer = malloc(file_size);
    if (!wav_buffer) {
        return -1;
    }

    unsigned char *p = wav_buffer;

    // RIFF头
    memcpy(p, "RIFF", 4); p += 4;
    *(uint32_t*)p = file_size - 8; p += 4;
    memcpy(p, "WAVE", 4); p += 4;

    // fmt子块
    memcpy(p, "fmt ", 4); p += 4;
    *(uint32_t*)p = 16; p += 4;  // fmt chunk size
    *(uint16_t*)p = 1; p += 2;   // audio format (PCM)
    *(uint16_t*)p = channels; p += 2;
    *(uint32_t*)p = sample_rate; p += 4;
    *(uint32_t*)p = sample_rate * channels * bits_per_sample / 8; p += 4; // byte rate
    *(uint16_t*)p = channels * bits_per_sample / 8; p += 2; // block align
    *(uint16_t*)p = bits_per_sample; p += 2;

    // data子块
    memcpy(p, "data", 4); p += 4;
    *(uint32_t*)p = data_size; p += 4;

    // 音频数据
    memcpy(p, g_aec_context.output_data, data_size);

    *wav_data = wav_buffer;
    *wav_size = file_size;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Generated WAV data: %d bytes (%d samples)", file_size, g_aec_context.output_samples);
    return 0;
}

/**
 * 保存输出数据到WAV文件
 */
static int save_output_to_wav_file(const char *filename) {
    if (!g_aec_context.output_data || g_aec_context.output_samples <= 0) {
        return -1;
    }

    FILE *file = fopen(filename, "wb");
    if (!file) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to open output file: %s", filename);
        return -1;
    }

    // WAV文件头
    const int sample_rate = AEC_SAMPLE_RATE;
    const int channels = 1;
    const int bits_per_sample = 16;
    const int data_size = g_aec_context.output_samples * sizeof(int16_t);
    const int file_size = 44 + data_size;

    // RIFF头
    fwrite("RIFF", 1, 4, file);
    int32_t chunk_size = file_size - 8;
    fwrite(&chunk_size, 4, 1, file);
    fwrite("WAVE", 1, 4, file);

    // fmt子块
    fwrite("fmt ", 1, 4, file);
    int32_t fmt_size = 16;
    fwrite(&fmt_size, 4, 1, file);
    int16_t audio_format = 1; // PCM
    fwrite(&audio_format, 2, 1, file);
    int16_t num_channels = channels;
    fwrite(&num_channels, 2, 1, file);
    int32_t sample_rate_val = sample_rate;
    fwrite(&sample_rate_val, 4, 1, file);
    int32_t byte_rate = sample_rate * channels * bits_per_sample / 8;
    fwrite(&byte_rate, 4, 1, file);
    int16_t block_align = channels * bits_per_sample / 8;
    fwrite(&block_align, 2, 1, file);
    int16_t bits_per_sample_val = bits_per_sample;
    fwrite(&bits_per_sample_val, 2, 1, file);

    // data子块
    fwrite("data", 1, 4, file);
    int32_t data_size_val = data_size;
    fwrite(&data_size_val, 4, 1, file);

    // 音频数据
    fwrite(g_aec_context.output_data, sizeof(int16_t), g_aec_context.output_samples, file);

    fclose(file);
    LOG_INFO(MODULE_AUDIO_REALTIME, "Output saved to WAV file: %s (%d samples)", filename, g_aec_context.output_samples);
    return 0;
}

/**
 * 广播AEC处理进度
 */
void aec_broadcast_progress(int progress, int total_frames) {
    websocket_broadcast_aec_progress(progress, total_frames);
}

/**
 * 广播AEC处理完成（包含数据）
 */
void aec_broadcast_complete_with_data(const char *output_data, int samples, int success) {
    websocket_broadcast_aec_complete_with_data(output_data, samples, success);
}

/**
 * AEC处理线程函数
 */
static void* aec_processing_thread(void* arg) {
    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC processing thread started");

    // 初始化Speex回声消除器
    SpeexEchoState *echo_state = speex_echo_state_init(AEC_FRAME_SIZE, AEC_TAIL_LENGTH);
    SpeexPreprocessState *preprocess_state = speex_preprocess_state_init(AEC_FRAME_SIZE, AEC_SAMPLE_RATE);

    if (!echo_state || !preprocess_state) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to initialize Speex AEC");
        g_aec_context.is_processing = 0;
        return NULL;
    }

    // 设置预处理器与回声消除器关联
    speex_preprocess_ctl(preprocess_state, SPEEX_PREPROCESS_SET_ECHO_STATE, echo_state);

    // 分配输出缓冲区
    int min_samples = (g_aec_context.mic_samples < g_aec_context.ref_samples) ?
                      g_aec_context.mic_samples : g_aec_context.ref_samples;
    g_aec_context.output_samples = min_samples;
    g_aec_context.output_data = malloc(g_aec_context.output_samples * sizeof(int16_t));

    if (!g_aec_context.output_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate output buffer");
        speex_echo_state_destroy(echo_state);
        speex_preprocess_state_destroy(preprocess_state);
        g_aec_context.is_processing = 0;
        return NULL;
    }

    // 处理音频帧
    int frame_count = 0;
    int total_frames = min_samples / AEC_FRAME_SIZE;
    g_aec_context.total_frames = total_frames;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Processing %d frames (%d samples each)", total_frames, AEC_FRAME_SIZE);

    for (int i = 0; i < total_frames && g_aec_context.is_processing; i++) {
        int offset = i * AEC_FRAME_SIZE;

        // 确保不超出缓冲区边界
        if (offset + AEC_FRAME_SIZE > g_aec_context.mic_samples ||
            offset + AEC_FRAME_SIZE > g_aec_context.ref_samples) {
            break;
        }

        // 执行回声消除
        speex_echo_cancellation(echo_state,
                               g_aec_context.mic_data + offset,
                               g_aec_context.ref_data + offset,
                               g_aec_context.output_data + offset);

        // 执行预处理（降噪等）
        speex_preprocess_run(preprocess_state, g_aec_context.output_data + offset);

        frame_count++;
        g_aec_context.progress = frame_count;

        // 每处理10帧广播一次进度
        if (frame_count % 10 == 0 || frame_count == total_frames) {
            aec_broadcast_progress(frame_count, total_frames);
        }

        // 模拟处理时间，避免过快
        usleep(1000); // 1ms延迟
    }

    // 清理资源
    speex_echo_state_destroy(echo_state);
    speex_preprocess_state_destroy(preprocess_state);

    if (g_aec_context.is_processing) {
        // 处理完成，数据已保存在g_aec_context.output_data中
        LOG_INFO(MODULE_AUDIO_REALTIME, "AEC processing completed successfully, %d samples generated",
                g_aec_context.output_samples);

        // 发送完成通知，告知前端可以通过HTTP获取结果
        aec_broadcast_complete_with_data(NULL, g_aec_context.output_samples, 1);
    } else {
        LOG_INFO(MODULE_AUDIO_REALTIME, "AEC processing was stopped");
        aec_broadcast_complete_with_data(NULL, 0, 0);
    }

    g_aec_context.is_processing = 0;
    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC processing thread finished");
    return NULL;
}

/**
 * 处理开始麦克风上传消息
 */
int aec_handle_start_mic_upload(const char *message, char *response, size_t response_size) {
    json_object *root = json_tokener_parse(message);
    if (!root) {
        snprintf(response, response_size, "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Invalid JSON\"}");
        return -1;
    }

    json_object *total_chunks_obj, *samples_obj;

    if (json_object_object_get_ex(root, "total_chunks", &total_chunks_obj) &&
        json_object_object_get_ex(root, "samples", &samples_obj)) {

        int total_chunks = json_object_get_int(total_chunks_obj);
        int samples = json_object_get_int(samples_obj);

        // 验证参数
        if (total_chunks <= 0 || total_chunks > 1000 || samples <= 0 || samples > MAX_AUDIO_DATA_SIZE) {
            snprintf(response, response_size,
                    "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Invalid parameters\"}");
        } else {
            // 清理之前的数据
            if (g_aec_context.mic_chunks) {
                for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
                    if (g_aec_context.mic_chunks[i]) free(g_aec_context.mic_chunks[i]);
                }
                free(g_aec_context.mic_chunks);
                free(g_aec_context.mic_chunk_sizes);
            }
            if (g_aec_context.mic_data) {
                free(g_aec_context.mic_data);
                g_aec_context.mic_data = NULL;
            }

            // 分配块指针数组
            g_aec_context.mic_chunks = calloc(total_chunks, sizeof(char*));
            g_aec_context.mic_chunk_sizes = calloc(total_chunks, sizeof(int));

            if (g_aec_context.mic_chunks && g_aec_context.mic_chunk_sizes) {
                g_aec_context.mic_expected_chunks = total_chunks;
                g_aec_context.mic_received_chunks = 0;
                g_aec_context.mic_samples_expected = samples;
                g_aec_context.mic_upload_complete = 0;

                snprintf(response, response_size,
                        "{\"type\":\"start_upload_response\",\"status\":\"success\",\"upload_type\":\"mic\"}");
            } else {
                snprintf(response, response_size,
                        "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
            }
        }
    } else {
        snprintf(response, response_size,
                "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Missing parameters\"}");
    }

    json_object_put(root);
    return 0;
}

/**
 * 处理麦克风数据块上传消息
 */
int aec_handle_upload_mic_chunk(const char *message, char *response, size_t response_size) {
    json_object *root = json_tokener_parse(message);
    if (!root) {
        snprintf(response, response_size, "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid JSON\"}");
        return -1;
    }

    json_object *chunk_index_obj, *chunk_data_obj;

    if (json_object_object_get_ex(root, "chunk_index", &chunk_index_obj) &&
        json_object_object_get_ex(root, "chunk_data", &chunk_data_obj)) {

        int chunk_index = json_object_get_int(chunk_index_obj);
        const char *chunk_data = json_object_get_string(chunk_data_obj);
        int chunk_size = chunk_data ? strlen(chunk_data) : 0;

        LOG_INFO(MODULE_AUDIO_REALTIME, "Receiving mic chunk %d/%d, size: %d",
                chunk_index, g_aec_context.mic_expected_chunks, chunk_size);

        if (!g_aec_context.mic_chunks) {
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Upload not initialized\"}");
        } else if (chunk_index < 0 || chunk_index >= g_aec_context.mic_expected_chunks) {
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk index\"}");
        } else if (chunk_size <= 0 || chunk_size > 65536) {
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk size\"}");
        } else if (g_aec_context.mic_chunks[chunk_index] != NULL) {
            // 重复的块，直接返回成功
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                    chunk_index, g_aec_context.mic_received_chunks, g_aec_context.mic_expected_chunks);
        } else {
            // 存储这个数据块
            g_aec_context.mic_chunks[chunk_index] = malloc(chunk_size + 1);
            if (!g_aec_context.mic_chunks[chunk_index]) {
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate memory for mic chunk %d", chunk_index);
                snprintf(response, response_size,
                        "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
            } else {
                memcpy(g_aec_context.mic_chunks[chunk_index], chunk_data, chunk_size);
                g_aec_context.mic_chunks[chunk_index][chunk_size] = '\0';
                g_aec_context.mic_chunk_sizes[chunk_index] = chunk_size;
                g_aec_context.mic_received_chunks++;

                snprintf(response, response_size,
                        "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                        chunk_index, g_aec_context.mic_received_chunks, g_aec_context.mic_expected_chunks);

                // 检查是否接收完所有块
                if (g_aec_context.mic_received_chunks >= g_aec_context.mic_expected_chunks) {
                    // 处理完成逻辑将在下一个函数中实现
                    aec_process_mic_complete(response, response_size);
                }
            }
        }
    } else {
        snprintf(response, response_size,
                "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Missing chunk data\"}");
    }

    json_object_put(root);
    return 0;
}

/**
 * 处理麦克风数据接收完成
 */
void aec_process_mic_complete(char *response, size_t response_size) {
    LOG_INFO(MODULE_AUDIO_REALTIME, "All mic chunks received, assembling data...");

    // 验证所有块都已接收
    int missing_chunks = 0;
    for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
        if (!g_aec_context.mic_chunks[i]) {
            missing_chunks++;
        }
    }

    if (missing_chunks > 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Missing %d mic chunks", missing_chunks);
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Missing chunks\"}");
        return;
    }

    // 计算总大小并拼接所有块
    int total_size = 0;
    for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
        total_size += g_aec_context.mic_chunk_sizes[i];
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Assembling %d chunks, total size: %d",
            g_aec_context.mic_expected_chunks, total_size);

    char *complete_data = malloc(total_size + 1);
    if (!complete_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate complete data buffer");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
        return;
    }

    int pos = 0;
    for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
        if (pos + g_aec_context.mic_chunk_sizes[i] <= total_size) {
            memcpy(complete_data + pos, g_aec_context.mic_chunks[i], g_aec_context.mic_chunk_sizes[i]);
            pos += g_aec_context.mic_chunk_sizes[i];
        }
    }
    complete_data[total_size] = '\0';

    // 解码完整数据
    g_aec_context.mic_data = malloc(g_aec_context.mic_samples_expected * sizeof(int16_t));
    if (!g_aec_context.mic_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate mic data buffer");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
        free(complete_data);
        return;
    }

    int decoded = base64_decode(complete_data, (unsigned char*)g_aec_context.mic_data,
                               g_aec_context.mic_samples_expected * sizeof(int16_t));

    LOG_INFO(MODULE_AUDIO_REALTIME, "Decoded %d bytes, expected %d",
            decoded, (int)(g_aec_context.mic_samples_expected * sizeof(int16_t)));

    if (decoded == (int)(g_aec_context.mic_samples_expected * sizeof(int16_t))) {
        g_aec_context.mic_samples = g_aec_context.mic_samples_expected;
        g_aec_context.mic_upload_complete = 1;
        LOG_INFO(MODULE_AUDIO_REALTIME, "Mic upload completed successfully");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"success\",\"upload_type\":\"mic\",\"samples\":%d}",
                g_aec_context.mic_samples);
    } else {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Base64 decode failed");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Decode failed\"}");
    }

    free(complete_data);

    // 清理临时块
    for (int i = 0; i < g_aec_context.mic_expected_chunks; i++) {
        if (g_aec_context.mic_chunks[i]) {
            free(g_aec_context.mic_chunks[i]);
            g_aec_context.mic_chunks[i] = NULL;
        }
    }
}

/**
 * 处理开始参考信号上传消息
 */
int aec_handle_start_ref_upload(const char *message, char *response, size_t response_size) {
    json_object *root = json_tokener_parse(message);
    if (!root) {
        snprintf(response, response_size, "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Invalid JSON\"}");
        return -1;
    }

    json_object *total_chunks_obj, *samples_obj;

    if (json_object_object_get_ex(root, "total_chunks", &total_chunks_obj) &&
        json_object_object_get_ex(root, "samples", &samples_obj)) {

        int total_chunks = json_object_get_int(total_chunks_obj);
        int samples = json_object_get_int(samples_obj);

        // 验证参数
        if (total_chunks <= 0 || total_chunks > 1000 || samples <= 0 || samples > MAX_AUDIO_DATA_SIZE) {
            snprintf(response, response_size,
                    "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Invalid parameters\"}");
        } else {
            // 清理之前的数据
            if (g_aec_context.ref_chunks) {
                for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
                    if (g_aec_context.ref_chunks[i]) free(g_aec_context.ref_chunks[i]);
                }
                free(g_aec_context.ref_chunks);
                free(g_aec_context.ref_chunk_sizes);
            }
            if (g_aec_context.ref_data) {
                free(g_aec_context.ref_data);
                g_aec_context.ref_data = NULL;
            }

            // 分配块指针数组
            g_aec_context.ref_chunks = calloc(total_chunks, sizeof(char*));
            g_aec_context.ref_chunk_sizes = calloc(total_chunks, sizeof(int));

            if (g_aec_context.ref_chunks && g_aec_context.ref_chunk_sizes) {
                g_aec_context.ref_expected_chunks = total_chunks;
                g_aec_context.ref_received_chunks = 0;
                g_aec_context.ref_samples_expected = samples;
                g_aec_context.ref_upload_complete = 0;

                snprintf(response, response_size,
                        "{\"type\":\"start_upload_response\",\"status\":\"success\",\"upload_type\":\"ref\"}");
            } else {
                snprintf(response, response_size,
                        "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
            }
        }
    } else {
        snprintf(response, response_size,
                "{\"type\":\"start_upload_response\",\"status\":\"error\",\"message\":\"Missing parameters\"}");
    }

    json_object_put(root);
    return 0;
}

/**
 * 处理参考信号数据块上传消息
 */
int aec_handle_upload_ref_chunk(const char *message, char *response, size_t response_size) {
    json_object *root = json_tokener_parse(message);
    if (!root) {
        snprintf(response, response_size, "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid JSON\"}");
        return -1;
    }

    json_object *chunk_index_obj, *chunk_data_obj;

    if (json_object_object_get_ex(root, "chunk_index", &chunk_index_obj) &&
        json_object_object_get_ex(root, "chunk_data", &chunk_data_obj)) {

        int chunk_index = json_object_get_int(chunk_index_obj);
        const char *chunk_data = json_object_get_string(chunk_data_obj);
        int chunk_size = chunk_data ? strlen(chunk_data) : 0;

        LOG_INFO(MODULE_AUDIO_REALTIME, "Receiving ref chunk %d/%d, size: %d",
                chunk_index, g_aec_context.ref_expected_chunks, chunk_size);

        if (!g_aec_context.ref_chunks) {
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Upload not initialized\"}");
        } else if (chunk_index < 0 || chunk_index >= g_aec_context.ref_expected_chunks) {
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk index\"}");
        } else if (chunk_size <= 0 || chunk_size > 65536) {
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Invalid chunk size\"}");
        } else if (g_aec_context.ref_chunks[chunk_index] != NULL) {
            // 重复的块，直接返回成功
            snprintf(response, response_size,
                    "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                    chunk_index, g_aec_context.ref_received_chunks, g_aec_context.ref_expected_chunks);
        } else {
            // 存储这个数据块
            g_aec_context.ref_chunks[chunk_index] = malloc(chunk_size + 1);
            if (!g_aec_context.ref_chunks[chunk_index]) {
                LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate memory for ref chunk %d", chunk_index);
                snprintf(response, response_size,
                        "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
            } else {
                memcpy(g_aec_context.ref_chunks[chunk_index], chunk_data, chunk_size);
                g_aec_context.ref_chunks[chunk_index][chunk_size] = '\0';
                g_aec_context.ref_chunk_sizes[chunk_index] = chunk_size;
                g_aec_context.ref_received_chunks++;

                snprintf(response, response_size,
                        "{\"type\":\"chunk_response\",\"status\":\"success\",\"chunk_index\":%d,\"received\":%d,\"total\":%d}",
                        chunk_index, g_aec_context.ref_received_chunks, g_aec_context.ref_expected_chunks);

                // 检查是否接收完所有块
                if (g_aec_context.ref_received_chunks >= g_aec_context.ref_expected_chunks) {
                    // 处理完成逻辑
                    aec_process_ref_complete(response, response_size);
                }
            }
        }
    } else {
        snprintf(response, response_size,
                "{\"type\":\"chunk_response\",\"status\":\"error\",\"message\":\"Missing chunk data\"}");
    }

    json_object_put(root);
    return 0;
}

/**
 * 处理参考信号数据接收完成
 */
void aec_process_ref_complete(char *response, size_t response_size) {
    LOG_INFO(MODULE_AUDIO_REALTIME, "All ref chunks received, assembling data...");

    // 验证所有块都已接收
    int missing_chunks = 0;
    for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
        if (!g_aec_context.ref_chunks[i]) {
            missing_chunks++;
        }
    }

    if (missing_chunks > 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Missing %d ref chunks", missing_chunks);
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Missing chunks\"}");
        return;
    }

    // 计算总大小并拼接所有块
    int total_size = 0;
    for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
        total_size += g_aec_context.ref_chunk_sizes[i];
    }

    LOG_INFO(MODULE_AUDIO_REALTIME, "Assembling %d ref chunks, total size: %d",
            g_aec_context.ref_expected_chunks, total_size);

    char *complete_data = malloc(total_size + 1);
    if (!complete_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate complete ref data buffer");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
        return;
    }

    int pos = 0;
    for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
        if (pos + g_aec_context.ref_chunk_sizes[i] <= total_size) {
            memcpy(complete_data + pos, g_aec_context.ref_chunks[i], g_aec_context.ref_chunk_sizes[i]);
            pos += g_aec_context.ref_chunk_sizes[i];
        }
    }
    complete_data[total_size] = '\0';

    // 解码完整数据
    g_aec_context.ref_data = malloc(g_aec_context.ref_samples_expected * sizeof(int16_t));
    if (!g_aec_context.ref_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate ref data buffer");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
        free(complete_data);
        return;
    }

    int decoded = base64_decode(complete_data, (unsigned char*)g_aec_context.ref_data,
                               g_aec_context.ref_samples_expected * sizeof(int16_t));

    LOG_INFO(MODULE_AUDIO_REALTIME, "Decoded %d bytes, expected %d",
            decoded, (int)(g_aec_context.ref_samples_expected * sizeof(int16_t)));

    if (decoded == (int)(g_aec_context.ref_samples_expected * sizeof(int16_t))) {
        g_aec_context.ref_samples = g_aec_context.ref_samples_expected;
        g_aec_context.ref_upload_complete = 1;
        LOG_INFO(MODULE_AUDIO_REALTIME, "Ref upload completed successfully");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"success\",\"upload_type\":\"ref\",\"samples\":%d}",
                g_aec_context.ref_samples);
    } else {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Base64 decode failed for ref data");
        snprintf(response, response_size,
                "{\"type\":\"upload_complete\",\"status\":\"error\",\"message\":\"Decode failed\"}");
    }

    free(complete_data);

    // 清理临时块
    for (int i = 0; i < g_aec_context.ref_expected_chunks; i++) {
        if (g_aec_context.ref_chunks[i]) {
            free(g_aec_context.ref_chunks[i]);
            g_aec_context.ref_chunks[i] = NULL;
        }
    }
}

/**
 * 处理开始AEC处理消息
 */
int aec_handle_start_processing(const char *message __attribute__((unused)), char *response, size_t response_size) {
    if (g_aec_context.is_processing) {
        snprintf(response, response_size,
                "{\"type\":\"aec_response\",\"status\":\"error\",\"message\":\"AEC processing already in progress\"}");
        return -1;
    }

    if (!g_aec_context.mic_upload_complete || !g_aec_context.ref_upload_complete) {
        snprintf(response, response_size,
                "{\"type\":\"aec_response\",\"status\":\"error\",\"message\":\"Audio data not ready\"}");
        return -1;
    }

    // 启动AEC处理线程
    g_aec_context.is_processing = 1;
    g_aec_context.progress = 0;
    g_aec_context.total_frames = g_aec_context.mic_samples / AEC_FRAME_SIZE;

    // 创建处理线程
    if (pthread_create(&g_aec_context.processing_thread, NULL, aec_processing_thread, NULL) != 0) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to create AEC processing thread");
        g_aec_context.is_processing = 0;
        snprintf(response, response_size,
                "{\"type\":\"aec_response\",\"status\":\"error\",\"message\":\"Failed to start processing thread\"}");
        return -1;
    }

    snprintf(response, response_size,
            "{\"type\":\"aec_response\",\"status\":\"started\",\"total_frames\":%d,\"mic_samples\":%d,\"ref_samples\":%d}",
            g_aec_context.total_frames, g_aec_context.mic_samples, g_aec_context.ref_samples);

    LOG_INFO(MODULE_AUDIO_REALTIME, "AEC processing started");
    return 0;
}

/**
 * 处理停止AEC处理消息
 */
int aec_handle_stop_processing(const char *message __attribute__((unused)), char *response, size_t response_size) {
    if (g_aec_context.is_processing) {
        g_aec_context.is_processing = 0;

        // 等待处理线程结束
        if (g_aec_context.processing_thread) {
            pthread_join(g_aec_context.processing_thread, NULL);
            g_aec_context.processing_thread = 0;
        }

        snprintf(response, response_size,
                "{\"type\":\"aec_response\",\"status\":\"stopped\"}");
        LOG_INFO(MODULE_AUDIO_REALTIME, "AEC processing stopped");
    } else {
        snprintf(response, response_size,
                "{\"type\":\"aec_response\",\"status\":\"not_running\"}");
    }
    return 0;
}

/**
 * 处理获取AEC状态消息
 */
int aec_handle_get_status(const char *message __attribute__((unused)), char *response, size_t response_size) {
    snprintf(response, response_size,
            "{\"type\":\"aec_status_response\",\"is_processing\":%s,\"progress\":%d,\"total_frames\":%d,\"mic_ready\":%s,\"ref_ready\":%s}",
            g_aec_context.is_processing ? "true" : "false",
            g_aec_context.progress,
            g_aec_context.total_frames,
            g_aec_context.mic_upload_complete ? "true" : "false",
            g_aec_context.ref_upload_complete ? "true" : "false");
    return 0;
}

/**
 * 处理获取AEC结果消息
 */
int aec_handle_get_result(const char *message __attribute__((unused)), char *response, size_t response_size) {
    if (!g_aec_context.output_data || g_aec_context.output_samples <= 0) {
        snprintf(response, response_size,
                "{\"type\":\"aec_result_response\",\"status\":\"error\",\"message\":\"No result data available\"}");
        return -1;
    }

    // 编码输出数据为base64
    int data_size = g_aec_context.output_samples * sizeof(int16_t);
    char *encoded_data = malloc(data_size * 2);
    if (!encoded_data) {
        snprintf(response, response_size,
                "{\"type\":\"aec_result_response\",\"status\":\"error\",\"message\":\"Memory allocation failed\"}");
        return -1;
    }

    int encoded_len = base64_encode((unsigned char*)g_aec_context.output_data,
                                   data_size, encoded_data, data_size * 2);

    if (encoded_len > 0) {
        // 检查数据大小，分块发送
        const int max_chunk_size = 16384; // 16KB chunks
        int total_chunks = (encoded_len + max_chunk_size - 1) / max_chunk_size;

        // 先发送开始响应
        snprintf(response, response_size,
                "{\"type\":\"aec_result_response\",\"status\":\"chunked\",\"samples\":%d,\"total_chunks\":%d}",
                g_aec_context.output_samples, total_chunks);

        // 启动分块发送
        aec_start_chunked_result_send(encoded_data, encoded_len, total_chunks);

        LOG_INFO(MODULE_AUDIO_REALTIME, "Starting chunked result send: %d chunks", total_chunks);
        return 0;
    } else {
        free(encoded_data);
        snprintf(response, response_size,
                "{\"type\":\"aec_result_response\",\"status\":\"error\",\"message\":\"Failed to encode result data\"}");
        return -1;
    }
}

/**
 * 启动分块发送结果数据
 */
static void aec_start_chunked_result_send(const char *encoded_data, int data_len, int total_chunks) {
    const int chunk_size = 16384; // 16KB chunks

    for (int chunk = 0; chunk < total_chunks; chunk++) {
        int start = chunk * chunk_size;
        int end = (start + chunk_size < data_len) ? start + chunk_size : data_len;
        int current_chunk_size = end - start;

        // 创建分块消息
        char *chunk_message = malloc(current_chunk_size + 200);
        if (chunk_message) {
            snprintf(chunk_message, current_chunk_size + 200,
                    "{\"type\":\"aec_result_chunk\",\"chunk\":%d,\"total_chunks\":%d,\"data\":\"",
                    chunk, total_chunks);

            int header_len = strlen(chunk_message);
            memcpy(chunk_message + header_len, encoded_data + start, current_chunk_size);
            strcpy(chunk_message + header_len + current_chunk_size, "\"}");

            // 广播这个分块
            websocket_broadcast_message(chunk_message);

            free(chunk_message);

            // 短暂延迟避免消息过快
            usleep(10000); // 10ms
        }
    }

    // 发送完成通知
    char complete_message[256];
    snprintf(complete_message, sizeof(complete_message),
            "{\"type\":\"aec_result_complete\",\"total_chunks\":%d}", total_chunks);
    websocket_broadcast_message(complete_message);

    // 释放编码数据
    free((void*)encoded_data);

    LOG_INFO(MODULE_AUDIO_REALTIME, "Chunked result send completed: %d chunks", total_chunks);
}

/**
 * 从文件加载麦克风数据
 */
int aec_load_mic_file(const char *filename) {
    LOG_INFO(MODULE_AUDIO_REALTIME, "Attempting to load mic file: %s", filename);

    FILE *file = fopen(filename, "rb");
    if (!file) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to open mic file: %s (errno: %d)", filename, errno);
        return -1;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    LOG_INFO(MODULE_AUDIO_REALTIME, "Mic file size: %ld bytes, MAX_AUDIO_DATA_SIZE: %d", file_size, MAX_AUDIO_DATA_SIZE);

    if (file_size <= 0 || file_size > MAX_AUDIO_DATA_SIZE) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Invalid mic file size: %ld (must be > 0 and <= %d)", file_size, MAX_AUDIO_DATA_SIZE);
        fclose(file);
        return -1;
    }

    // 分配内存
    if (g_aec_context.mic_data) {
        free(g_aec_context.mic_data);
    }

    g_aec_context.mic_data = malloc(file_size);
    if (!g_aec_context.mic_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate mic data buffer");
        fclose(file);
        return -1;
    }

    // 读取文件
    size_t read_size = fread(g_aec_context.mic_data, 1, file_size, file);
    fclose(file);

    if (read_size != file_size) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to read mic file completely");
        free(g_aec_context.mic_data);
        g_aec_context.mic_data = NULL;
        return -1;
    }

    g_aec_context.mic_samples = file_size / sizeof(int16_t);
    g_aec_context.mic_upload_complete = 1;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Loaded mic file: %s (%d samples)", filename, g_aec_context.mic_samples);
    return 0;
}

/**
 * 从文件加载参考信号数据
 */
int aec_load_ref_file(const char *filename) {
    FILE *file = fopen(filename, "rb");
    if (!file) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to open ref file: %s", filename);
        return -1;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    if (file_size <= 0 || file_size > MAX_AUDIO_DATA_SIZE) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Invalid ref file size: %ld", file_size);
        fclose(file);
        return -1;
    }

    // 分配内存
    if (g_aec_context.ref_data) {
        free(g_aec_context.ref_data);
    }

    g_aec_context.ref_data = malloc(file_size);
    if (!g_aec_context.ref_data) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to allocate ref data buffer");
        fclose(file);
        return -1;
    }

    // 读取文件
    size_t read_size = fread(g_aec_context.ref_data, 1, file_size, file);
    fclose(file);

    if (read_size != file_size) {
        LOG_ERROR(MODULE_AUDIO_REALTIME, "Failed to read ref file completely");
        free(g_aec_context.ref_data);
        g_aec_context.ref_data = NULL;
        return -1;
    }

    g_aec_context.ref_samples = file_size / sizeof(int16_t);
    g_aec_context.ref_upload_complete = 1;

    LOG_INFO(MODULE_AUDIO_REALTIME, "Loaded ref file: %s (%d samples)", filename, g_aec_context.ref_samples);
    return 0;
}

#endif