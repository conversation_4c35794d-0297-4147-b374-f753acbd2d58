#ifndef VOICE_EVENTS_H
#define VOICE_EVENTS_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>



/**
 * 语音事件处理器
 * 
 * 这个模块处理语音对话系统的所有事件回调：
 * 1. onReady() - 系统就绪
 * 2. onStart() - VAD检测到人声，开始录音
 * 3. onProcessAudio(bytes, size) - 每100ms产生OPUS数据
 * 4. onFinish(bytes, size) - 完整的OPUS数据
 * 5. onStop() - 连续5秒无声音，停止录音
 * 6. onClose() - 关闭连接
 */

/**
 * 初始化语音事件处理器
 * @param enable_ali_asr 是否启用Ali ASR
 * @return 0成功，-1失败
 */
int voice_events_init(bool enable_ali_asr);

/**
 * 清理语音事件处理器
 */
void voice_events_cleanup(void);


/**
 * 事件回调函数
 */
void on_voice_ready(void);
void on_voice_start(void);
void on_voice_process_audio(const uint8_t *opus_data, size_t opus_size,int sample_rate, int channels);
void on_voice_finish(const uint8_t *opus_data, size_t opus_size,int sample_rate, int channels);
void on_voice_stop(void);
void on_voice_close(void);

/**
 * 设置语音事件回调到voice_dialog系统
 * @param voice_dialog 语音对话处理器
 * @return 0成功，-1失败
 */
int voice_events_setup_callbacks(void *voice_dialog);

#endif // VOICE_EVENTS_H
